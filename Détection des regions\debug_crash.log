[2025-05-27 22:11:19] 🔧 Configuration de la surveillance des crashes
[2025-05-27 22:11:19] ✅ Surveillance des crashes configurée
[2025-05-27 22:11:19] 🚀 Application detector_gui.py démarrée avec surveillance des crashes
[2025-05-27 22:11:19] 🧹 Nettoyage à la sortie du programme
[2025-05-27 22:11:19] 📊 Informations système finales:
[2025-05-27 22:11:19]    CPU: 0.0%
[2025-05-27 22:11:19]    RAM: 31.3%
[2025-05-27 22:11:19] 👋 Fin du programme
[2025-05-27 22:12:51] 🔧 Configuration de la surveillance des crashes
[2025-05-27 22:12:51] ✅ Surveillance des crashes configurée
[2025-05-27 22:12:51] 🚀 Application detector_gui.py démarrée avec surveillance des crashes
[2025-05-27 22:12:56] 🚪 Événement de fermeture de l'application détecté
[2025-05-27 22:12:56] ✅ Fermeture propre de l'application terminée
[2025-05-27 22:13:02] 🚪 Événement de fermeture de l'application détecté
[2025-05-27 22:13:02] 🛑 Arrêt de la surveillance système
[2025-05-27 22:13:03] ⏰ Arrêt du timer de surveillance
[2025-05-27 22:13:03] ✅ Fermeture propre de l'application terminée
[2025-05-27 22:13:08] 🚪 Événement de fermeture de l'application détecté
[2025-05-27 22:13:08] 🛑 Arrêt de la surveillance système
[2025-05-27 22:13:10] ⏰ Arrêt du timer de surveillance
[2025-05-27 22:13:10] ✅ Fermeture propre de l'application terminée
[2025-05-27 22:13:10] 🧹 Nettoyage à la sortie du programme
[2025-05-27 22:13:10] 📊 Informations système finales:
[2025-05-27 22:13:10]    CPU: 10.7%
[2025-05-27 22:13:10]    RAM: 34.4%
[2025-05-27 22:13:10] 👋 Fin du programme
[2025-05-27 22:14:01] 🔧 Configuration de la surveillance des crashes
[2025-05-27 22:14:01] ✅ Surveillance des crashes configurée
[2025-05-27 22:14:01] 🚀 Application detector_gui.py démarrée avec surveillance des crashes
[2025-05-27 22:15:13] 🎯 Début de la détection des cartes
[2025-05-27 22:15:47] 🎯 Début de la détection des cartes
[2025-05-27 22:16:17] 🎯 Début de la détection des cartes
[2025-05-27 22:17:04] 🎯 Début de la détection des cartes
[2025-05-27 22:17:57] 🎯 Début de la détection des cartes
[2025-05-27 22:19:08] 🎯 Début de la détection des cartes
[2025-05-27 22:19:48] 🎯 Début de la détection des cartes
[2025-05-27 22:20:10] 🎯 Début de la détection des cartes
[2025-05-27 22:20:39] 🎯 Début de la détection des cartes
[2025-05-27 22:21:00] 🎯 Début de la détection des cartes
[2025-05-27 22:21:59] 🎯 Début de la détection des cartes
[2025-05-27 22:22:58] 🎯 Début de la détection des cartes
[2025-05-27 22:23:21] 🎯 Début de la détection des cartes
[2025-05-27 22:23:50] 🎯 Début de la détection des cartes
[2025-05-27 22:24:20] 🎯 Début de la détection des cartes
[2025-05-27 22:24:55] 🎯 Début de la détection des cartes
[2025-05-27 22:25:16] 🎯 Début de la détection des cartes
[2025-05-27 22:25:38] 🎯 Début de la détection des cartes
[2025-05-27 22:25:57] 🔧 Configuration de la surveillance des crashes
[2025-05-27 22:25:57] ✅ Surveillance des crashes configurée
[2025-05-27 22:25:57] 🚀 Application detector_gui.py démarrée avec surveillance des crashes
[2025-05-27 22:26:43] 🎯 Début de la détection des cartes
[2025-05-27 22:27:21] 🎯 Début de la détection des cartes
[2025-05-27 22:28:07] 🎯 Début de la détection des cartes
[2025-05-27 22:28:50] 🎯 Début de la détection des cartes
[2025-05-27 22:29:08] 🎯 Début de la détection des cartes
[2025-05-27 22:29:22] 🎯 Début de la détection des cartes
[2025-05-27 22:33:40] 🔧 Configuration de la surveillance des crashes
[2025-05-27 22:33:40] ✅ Surveillance des crashes configurée
[2025-05-27 22:33:40] 🚀 Application detector_gui.py démarrée avec surveillance des crashes
[2025-05-27 22:33:45] 🧹 Nettoyage à la sortie du programme
[2025-05-27 22:33:45] 📊 Informations système finales:
[2025-05-27 22:33:45]    CPU: 10.8%
[2025-05-27 22:33:45]    RAM: 29.4%
[2025-05-27 22:33:45] 👋 Fin du programme
[2025-05-27 22:34:34] 🔧 Configuration de la surveillance des crashes
[2025-05-27 22:34:34] ✅ Surveillance des crashes configurée
[2025-05-27 22:34:34] 🚀 Application detector_gui.py démarrée avec surveillance des crashes
[2025-05-27 22:34:41] 🧹 Nettoyage à la sortie du programme
[2025-05-27 22:34:41] 📊 Informations système finales:
[2025-05-27 22:34:41]    CPU: 13.9%
[2025-05-27 22:34:41]    RAM: 30.7%
[2025-05-27 22:34:41] 👋 Fin du programme
[2025-05-27 22:34:41] 🔧 Configuration de la surveillance des crashes
[2025-05-27 22:34:41] ✅ Surveillance des crashes configurée
[2025-05-27 22:34:41] 🚀 Application detector_gui.py démarrée avec surveillance des crashes
[2025-05-27 22:34:56] 🎯 Début de la détection des cartes
[2025-05-27 22:35:38] 🔧 Configuration de la surveillance des crashes
[2025-05-27 22:35:38] ✅ Surveillance des crashes configurée
[2025-05-27 22:35:38] 🚀 Application detector_gui.py démarrée avec surveillance des crashes
[2025-05-27 22:35:54] 🧹 Nettoyage à la sortie du programme
[2025-05-27 22:35:54] 📊 Informations système finales:
[2025-05-27 22:35:54]    CPU: 4.3%
[2025-05-27 22:35:54]    RAM: 29.5%
[2025-05-27 22:35:54] 👋 Fin du programme
[2025-05-27 22:36:20] 🔧 Configuration de la surveillance des crashes
[2025-05-27 22:36:20] ✅ Surveillance des crashes configurée
[2025-05-27 22:36:20] 🚀 Application detector_gui.py démarrée avec surveillance des crashes
[2025-05-27 22:36:26] 🧹 Nettoyage à la sortie du programme
[2025-05-27 22:36:26] 📊 Informations système finales:
[2025-05-27 22:36:26]    CPU: 8.2%
[2025-05-27 22:36:26]    RAM: 29.3%
[2025-05-27 22:36:26] 👋 Fin du programme
[2025-05-27 22:36:55] 🔧 Configuration de la surveillance des crashes
[2025-05-27 22:36:55] ✅ Surveillance des crashes configurée
[2025-05-27 22:36:55] 🚀 Application detector_gui.py démarrée avec surveillance des crashes
[2025-05-27 22:37:00] 🧹 Nettoyage à la sortie du programme
[2025-05-27 22:37:00] 📊 Informations système finales:
[2025-05-27 22:37:00]    CPU: 7.3%
[2025-05-27 22:37:00]    RAM: 29.4%
[2025-05-27 22:37:00] 👋 Fin du programme
[2025-05-27 22:38:01] 🔧 Configuration de la surveillance des crashes
[2025-05-27 22:38:01] ✅ Surveillance des crashes configurée
[2025-05-27 22:38:01] 🚀 Application detector_gui.py démarrée avec surveillance des crashes
[2025-05-27 22:38:06] 🧹 Nettoyage à la sortie du programme
[2025-05-27 22:38:06] 📊 Informations système finales:
[2025-05-27 22:38:06]    CPU: 7.8%
[2025-05-27 22:38:06]    RAM: 29.4%
[2025-05-27 22:38:06] 👋 Fin du programme
[2025-05-27 22:38:39] 🔧 Configuration de la surveillance des crashes
[2025-05-27 22:38:39] ✅ Surveillance des crashes configurée
[2025-05-27 22:38:39] 🚀 Application detector_gui.py démarrée avec surveillance des crashes
[2025-05-27 22:38:44] 🧹 Nettoyage à la sortie du programme
[2025-05-27 22:38:44] 📊 Informations système finales:
[2025-05-27 22:38:44]    CPU: 6.8%
[2025-05-27 22:38:44]    RAM: 29.5%
[2025-05-27 22:38:44] 👋 Fin du programme
[2025-05-27 22:39:33] 🔧 Configuration de la surveillance des crashes
[2025-05-27 22:39:33] ✅ Surveillance des crashes configurée
[2025-05-27 22:39:33] 🚀 Application detector_gui.py démarrée avec surveillance des crashes
[2025-05-27 22:39:41] 🚨 SIGNAL REÇU: 2
[2025-05-27 22:39:41] 📍 Frame: <frame at 0x00000276D4009340, file 'C:\\Users\\<USER>\\PokerAdvisor\\Détection des regions\\detector_gui.py', line 504, code __init__>
[2025-05-27 22:39:41] 🔍 Stack trace:
[2025-05-27 22:39:41]    File "C:\Users\<USER>\PokerAdvisor\Détection des regions\test_quinte_correction.py", line 169, in <module>
    main()
[2025-05-27 22:39:41]    File "C:\Users\<USER>\PokerAdvisor\Détection des regions\test_quinte_correction.py", line 159, in main
    if test_straight_detection():
[2025-05-27 22:39:41]    File "C:\Users\<USER>\PokerAdvisor\Détection des regions\test_quinte_correction.py", line 25, in test_straight_detection
    detector_gui = DetectorGUI()
[2025-05-27 22:39:41]    File "C:\Users\<USER>\PokerAdvisor\Détection des regions\detector_gui.py", line 504, in __init__
    QMessageBox.information(self, "Bienvenue dans Poker Advisor",
[2025-05-27 22:39:41] 💀 Application interrompue par signal
[2025-05-27 22:39:41] 🧹 Nettoyage à la sortie du programme
[2025-05-27 22:39:41] 📊 Informations système finales:
[2025-05-27 22:39:41]    CPU: 7.7%
[2025-05-27 22:39:41]    RAM: 29.9%
[2025-05-27 22:39:41] 👋 Fin du programme
[2025-05-27 22:52:44] 🔧 Configuration de la surveillance des crashes
[2025-05-27 22:52:44] ✅ Surveillance des crashes configurée
[2025-05-27 22:52:44] 🚀 Application detector_gui.py démarrée avec surveillance des crashes
[2025-05-27 22:53:03] 🎯 Début de la détection des cartes
[2025-05-27 22:58:35] 🔧 Configuration de la surveillance des crashes
[2025-05-27 22:58:35] ✅ Surveillance des crashes configurée
[2025-05-27 22:58:35] 🚀 Application detector_gui.py démarrée avec surveillance des crashes
[2025-05-27 22:58:51] 🎯 Début de la détection des cartes
[2025-05-27 23:01:38] 🔧 Configuration de la surveillance des crashes
[2025-05-27 23:01:38] ✅ Surveillance des crashes configurée
[2025-05-27 23:01:38] 🚀 Application detector_gui.py démarrée avec surveillance des crashes
[2025-05-27 23:01:56] 🧹 Nettoyage à la sortie du programme
[2025-05-27 23:01:56] 📊 Informations système finales:
[2025-05-27 23:01:56]    CPU: 10.6%
[2025-05-27 23:01:56]    RAM: 29.6%
[2025-05-27 23:01:56] 👋 Fin du programme
[2025-05-27 23:04:05] 🔧 Configuration de la surveillance des crashes
[2025-05-27 23:04:05] ✅ Surveillance des crashes configurée
[2025-05-27 23:04:05] 🚀 Application detector_gui.py démarrée avec surveillance des crashes
[2025-05-27 23:05:18] 🎯 Début de la détection des cartes
[2025-05-27 23:05:32] 🚪 Événement de fermeture de l'application détecté
[2025-05-27 23:05:32] ✅ Fermeture propre de l'application terminée
[2025-05-27 23:05:44] 🔧 Configuration de la surveillance des crashes
[2025-05-27 23:05:44] ✅ Surveillance des crashes configurée
[2025-05-27 23:05:44] 🚀 Application detector_gui.py démarrée avec surveillance des crashes
[2025-05-27 23:06:08] 🎯 Début de la détection des cartes
[2025-05-27 23:07:45] 🚨 SIGNAL REÇU: 2
[2025-05-27 23:07:45] 📍 Frame: <frame at 0x000001A043EA6810, file 'C:\\Users\\<USER>\\PokerAdvisor\\Détection des regions\\lancer_detector_safe.py', line 170, code main>
[2025-05-27 23:07:45] 🔍 Stack trace:
[2025-05-27 23:07:45]    File "C:\Users\<USER>\PokerAdvisor\Détection des regions\lancer_detector_safe.py", line 173, in <module>
    main()
[2025-05-27 23:07:45]    File "C:\Users\<USER>\PokerAdvisor\Détection des regions\lancer_detector_safe.py", line 170, in main
    input("Appuyez sur Entrée pour fermer...")
[2025-05-27 23:07:45] 💀 Application interrompue par signal
[2025-05-27 23:07:45] 💥 EXCEPTION NON CAPTURÉE:
[2025-05-27 23:07:45]    Type: EOFError
[2025-05-27 23:07:45]    Valeur: 
[2025-05-27 23:07:45] 🔍 Traceback complet:
[2025-05-27 23:07:45]    Traceback (most recent call last):
[2025-05-27 23:07:45]    File "C:\Users\<USER>\PokerAdvisor\Détection des regions\lancer_detector_safe.py", line 173, in <module>
[2025-05-27 23:07:45]        main()
[2025-05-27 23:07:45]    File "C:\Users\<USER>\PokerAdvisor\Détection des regions\lancer_detector_safe.py", line 170, in main
[2025-05-27 23:07:45]        input("Appuyez sur Entrée pour fermer...")
[2025-05-27 23:07:45]    EOFError
[2025-05-27 23:07:45] 🧹 Nettoyage à la sortie du programme
[2025-05-27 23:07:45] 📊 Informations système finales:
[2025-05-27 23:07:45]    CPU: 4.4%
[2025-05-27 23:07:45]    RAM: 32.2%
[2025-05-27 23:07:45] 👋 Fin du programme
[2025-05-27 23:08:42] 🔧 Configuration de la surveillance des crashes
[2025-05-27 23:08:42] ✅ Surveillance des crashes configurée
[2025-05-27 23:08:42] 🚀 Application detector_gui.py démarrée avec surveillance des crashes
[2025-05-27 23:08:53] 🎯 Début de la détection des cartes
[2025-05-27 23:09:47] 🎯 Début de la détection des cartes
[2025-05-27 23:10:45] 🎯 Début de la détection des cartes
[2025-05-27 23:11:28] 🎯 Début de la détection des cartes
[2025-05-27 23:24:36] 🔧 Configuration de la surveillance des crashes
[2025-05-27 23:24:36] ✅ Surveillance des crashes configurée
[2025-05-27 23:24:36] 🚀 Application detector_gui.py démarrée avec surveillance des crashes
[2025-05-27 23:24:48] 🎯 Début de la détection des cartes
[2025-05-27 23:25:11] 🚪 Événement de fermeture de l'application détecté
[2025-05-27 23:25:11] 🛑 Arrêt de la surveillance système
[2025-05-28 17:38:46] 🔧 Configuration de la surveillance des crashes
[2025-05-28 17:38:46] ✅ Surveillance des crashes configurée
[2025-05-28 17:38:46] 🚀 Application detector_gui.py démarrée avec surveillance des crashes
[2025-05-28 17:39:17] 🎯 Début de la détection des cartes
[2025-05-28 17:45:42] 🔧 Configuration de la surveillance des crashes
[2025-05-28 17:45:42] ✅ Surveillance des crashes configurée
[2025-05-28 17:45:42] 🚀 Application detector_gui.py démarrée avec surveillance des crashes
[2025-05-28 17:50:06] 🔧 Configuration de la surveillance des crashes
[2025-05-28 17:50:06] ✅ Surveillance des crashes configurée
[2025-05-28 17:50:06] 🚀 Application detector_gui.py démarrée avec surveillance des crashes
[2025-05-28 17:51:08] 🚪 Événement de fermeture de l'application détecté
[2025-05-28 17:51:08] ✅ Fermeture propre de l'application terminée
[2025-05-28 17:51:08] 🧹 Nettoyage à la sortie du programme
[2025-05-28 17:51:08] 📊 Informations système finales:
[2025-05-28 17:51:08]    CPU: 3.9%
[2025-05-28 17:51:08]    RAM: 28.3%
[2025-05-28 17:51:08] 👋 Fin du programme
[2025-05-28 17:51:36] 🔧 Configuration de la surveillance des crashes
[2025-05-28 17:51:36] ✅ Surveillance des crashes configurée
[2025-05-28 17:51:36] 🚀 Application detector_gui.py démarrée avec surveillance des crashes
[2025-05-28 17:51:53] 🎯 Début de la détection des cartes
[2025-05-28 17:55:24] 🎯 Début de la détection des cartes
[2025-05-28 17:55:57] 🎯 Début de la détection des cartes
[2025-05-28 18:01:05] 🎯 Début de la détection des cartes
[2025-05-28 18:02:31] 🎯 Début de la détection des cartes
[2025-05-28 18:08:16] 🔧 Configuration de la surveillance des crashes
[2025-05-28 18:08:16] ✅ Surveillance des crashes configurée
[2025-05-28 18:08:16] 🚀 Application detector_gui.py démarrée avec surveillance des crashes
[2025-05-28 18:09:14] 🎯 Début de la détection des cartes
[2025-05-28 18:10:48] 🎯 Début de la détection des cartes
[2025-05-28 18:11:44] 🎯 Début de la détection des cartes
[2025-05-28 18:15:06] 🎯 Début de la détection des cartes
[2025-05-28 18:18:45] 🔧 Configuration de la surveillance des crashes
[2025-05-28 18:18:45] ✅ Surveillance des crashes configurée
[2025-05-28 18:18:45] 🚀 Application detector_gui.py démarrée avec surveillance des crashes
[2025-05-28 18:19:28] 🎯 Début de la détection des cartes
[2025-05-28 18:19:38] 🚪 Événement de fermeture de l'application détecté
[2025-05-28 18:19:38] ✅ Fermeture propre de l'application terminée
[2025-05-28 18:19:38] 🧹 Nettoyage à la sortie du programme
[2025-05-28 18:19:38] 📊 Informations système finales:
[2025-05-28 18:19:38]    CPU: 8.7%
[2025-05-28 18:19:38]    RAM: 32.1%
[2025-05-28 18:19:38] 👋 Fin du programme
[2025-05-28 18:19:50] 🔧 Configuration de la surveillance des crashes
[2025-05-28 18:19:50] ✅ Surveillance des crashes configurée
[2025-05-28 18:19:50] 🚀 Application detector_gui.py démarrée avec surveillance des crashes
[2025-05-28 18:20:05] 🎯 Début de la détection des cartes
[2025-05-28 18:20:34] 🎯 Début de la détection des cartes
[2025-05-28 18:21:03] 🎯 Début de la détection des cartes
[2025-05-28 18:25:57] 🔧 Configuration de la surveillance des crashes
[2025-05-28 18:25:57] ✅ Surveillance des crashes configurée
[2025-05-28 18:25:57] 🚀 Application detector_gui.py démarrée avec surveillance des crashes
[2025-05-28 18:26:22] 🎯 Début de la détection des cartes
[2025-05-28 18:26:45] 🎯 Début de la détection des cartes
[2025-05-28 18:30:37] 🔧 Configuration de la surveillance des crashes
[2025-05-28 18:30:38] ✅ Surveillance des crashes configurée
[2025-05-28 18:30:38] 🚀 Application detector_gui.py démarrée avec surveillance des crashes
[2025-05-28 18:31:45] 🔧 Configuration de la surveillance des crashes
[2025-05-28 18:31:45] ✅ Surveillance des crashes configurée
[2025-05-28 18:31:45] 🚀 Application detector_gui.py démarrée avec surveillance des crashes
[2025-05-28 18:32:32] 🎯 Début de la détection des cartes
[2025-05-28 18:33:58] 🔧 Configuration de la surveillance des crashes
[2025-05-28 18:33:58] ✅ Surveillance des crashes configurée
[2025-05-28 18:33:58] 🚀 Application detector_gui.py démarrée avec surveillance des crashes
[2025-05-28 18:35:11] 🔧 Configuration de la surveillance des crashes
[2025-05-28 18:35:11] ✅ Surveillance des crashes configurée
[2025-05-28 18:35:11] 🚀 Application detector_gui.py démarrée avec surveillance des crashes
[2025-05-28 18:35:18] 🚪 Événement de fermeture de l'application détecté
[2025-05-28 18:35:18] ✅ Fermeture propre de l'application terminée
[2025-05-28 18:35:18] 🧹 Nettoyage à la sortie du programme
[2025-05-28 18:35:18] 📊 Informations système finales:
[2025-05-28 18:35:18]    CPU: 6.4%
[2025-05-28 18:35:18]    RAM: 32.4%
[2025-05-28 18:35:18] 👋 Fin du programme
[2025-05-28 18:35:28] 🔧 Configuration de la surveillance des crashes
[2025-05-28 18:35:28] ✅ Surveillance des crashes configurée
[2025-05-28 18:35:28] 🚀 Application detector_gui.py démarrée avec surveillance des crashes
[2025-05-28 18:36:03] 🎯 Début de la détection des cartes
[2025-05-28 18:37:01] 🔧 Configuration de la surveillance des crashes
[2025-05-28 18:37:01] ✅ Surveillance des crashes configurée
[2025-05-28 18:37:01] 🚀 Application detector_gui.py démarrée avec surveillance des crashes
[2025-05-28 18:37:59] 🔧 Configuration de la surveillance des crashes
[2025-05-28 18:37:59] ✅ Surveillance des crashes configurée
[2025-05-28 18:37:59] 🚀 Application detector_gui.py démarrée avec surveillance des crashes
[2025-05-28 18:38:15] 🎯 Début de la détection des cartes
[2025-05-28 18:38:40] 🎯 Début de la détection des cartes
[2025-05-28 18:40:54] 🔧 Configuration de la surveillance des crashes
[2025-05-28 18:40:54] ✅ Surveillance des crashes configurée
[2025-05-28 18:40:54] 🚀 Application detector_gui.py démarrée avec surveillance des crashes
[2025-05-28 18:41:11] 🎯 Début de la détection des cartes
[2025-05-28 18:41:25] 🎯 Début de la détection des cartes
[2025-05-28 18:41:52] 🚪 Événement de fermeture de l'application détecté
[2025-05-28 18:41:52] ✅ Fermeture propre de l'application terminée
[2025-05-28 18:41:52] 🧹 Nettoyage à la sortie du programme
[2025-05-28 18:41:52] 📊 Informations système finales:
[2025-05-28 18:41:52]    CPU: 12.4%
[2025-05-28 18:41:52]    RAM: 34.9%
[2025-05-28 18:41:52] 👋 Fin du programme
[2025-05-28 18:43:21] 🔧 Configuration de la surveillance des crashes
[2025-05-28 18:43:21] ✅ Surveillance des crashes configurée
[2025-05-28 18:43:21] 🚀 Application detector_gui.py démarrée avec surveillance des crashes
[2025-05-28 18:43:38] 🎯 Début de la détection des cartes
[2025-05-28 18:44:24] 🚪 Événement de fermeture de l'application détecté
[2025-05-28 18:44:24] ✅ Fermeture propre de l'application terminée
[2025-05-28 18:44:24] 🧹 Nettoyage à la sortie du programme
[2025-05-28 18:44:24] 📊 Informations système finales:
[2025-05-28 18:44:24]    CPU: 7.3%
[2025-05-28 18:44:24]    RAM: 34.1%
[2025-05-28 18:44:24] 👋 Fin du programme
[2025-05-28 18:44:33] 🔧 Configuration de la surveillance des crashes
[2025-05-28 18:44:33] ✅ Surveillance des crashes configurée
[2025-05-28 18:44:33] 🚀 Application detector_gui.py démarrée avec surveillance des crashes
[2025-05-28 18:45:26] 🎯 Début de la détection des cartes
[2025-05-28 18:45:52] 🎯 Début de la détection des cartes
[2025-05-28 18:46:08] 🎯 Début de la détection des cartes
[2025-05-28 18:46:23] 🎯 Début de la détection des cartes
[2025-05-28 18:46:43] 🎯 Début de la détection des cartes
[2025-05-28 18:47:11] 🎯 Début de la détection des cartes
[2025-05-28 18:47:40] 🎯 Début de la détection des cartes
[2025-05-28 18:47:57] 🎯 Début de la détection des cartes
[2025-05-28 18:48:18] 🎯 Début de la détection des cartes
[2025-05-28 18:49:29] 🎯 Début de la détection des cartes
[2025-05-28 18:49:46] 🎯 Début de la détection des cartes
[2025-05-28 18:50:02] 🎯 Début de la détection des cartes
[2025-05-28 18:50:22] 🎯 Début de la détection des cartes
[2025-05-28 18:59:19] 🔧 Configuration de la surveillance des crashes
[2025-05-28 18:59:19] ✅ Surveillance des crashes configurée
[2025-05-28 18:59:19] 🚀 Application detector_gui.py démarrée avec surveillance des crashes
[2025-05-28 19:02:15] 🔧 Configuration de la surveillance des crashes
[2025-05-28 19:02:15] ✅ Surveillance des crashes configurée
[2025-05-28 19:02:15] 🚀 Application detector_gui.py démarrée avec surveillance des crashes
[2025-05-28 19:03:11] 🚪 Événement de fermeture de l'application détecté
[2025-05-28 19:03:11] ✅ Fermeture propre de l'application terminée
[2025-05-28 19:03:11] 🧹 Nettoyage à la sortie du programme
[2025-05-28 19:03:11] 📊 Informations système finales:
[2025-05-28 19:03:11]    CPU: 5.8%
[2025-05-28 19:03:11]    RAM: 33.9%
[2025-05-28 19:03:11] 👋 Fin du programme
[2025-05-28 19:03:20] 🔧 Configuration de la surveillance des crashes
[2025-05-28 19:03:20] ✅ Surveillance des crashes configurée
[2025-05-28 19:03:20] 🚀 Application detector_gui.py démarrée avec surveillance des crashes
[2025-05-28 19:04:23] 🎯 Début de la détection des cartes
[2025-05-28 19:07:07] 🔧 Configuration de la surveillance des crashes
[2025-05-28 19:07:07] ✅ Surveillance des crashes configurée
[2025-05-28 19:07:07] 🚀 Application detector_gui.py démarrée avec surveillance des crashes
[2025-05-28 19:07:31] 🎯 Début de la détection des cartes
[2025-05-28 19:07:44] 🚪 Événement de fermeture de l'application détecté
[2025-05-28 19:07:44] ✅ Fermeture propre de l'application terminée
[2025-05-28 19:07:44] 🧹 Nettoyage à la sortie du programme
[2025-05-28 19:07:44] 📊 Informations système finales:
[2025-05-28 19:07:44]    CPU: 6.3%
[2025-05-28 19:07:44]    RAM: 34.6%
[2025-05-28 19:07:44] 👋 Fin du programme
[2025-05-28 19:08:35] 🔧 Configuration de la surveillance des crashes
[2025-05-28 19:08:35] ✅ Surveillance des crashes configurée
[2025-05-28 19:08:35] 🚀 Application detector_gui.py démarrée avec surveillance des crashes
[2025-05-28 19:10:27] 🔧 Configuration de la surveillance des crashes
[2025-05-28 19:10:27] ✅ Surveillance des crashes configurée
[2025-05-28 19:10:27] 🚀 Application detector_gui.py démarrée avec surveillance des crashes
[2025-05-28 19:11:50] 🚪 Événement de fermeture de l'application détecté
[2025-05-28 19:11:50] ✅ Fermeture propre de l'application terminée
[2025-05-28 19:11:50] 🧹 Nettoyage à la sortie du programme
[2025-05-28 19:11:50] 📊 Informations système finales:
[2025-05-28 19:11:50]    CPU: 4.5%
[2025-05-28 19:11:50]    RAM: 33.6%
[2025-05-28 19:11:50] 👋 Fin du programme
[2025-05-28 19:11:59] 🔧 Configuration de la surveillance des crashes
[2025-05-28 19:11:59] ✅ Surveillance des crashes configurée
[2025-05-28 19:11:59] 🚀 Application detector_gui.py démarrée avec surveillance des crashes
[2025-05-28 19:12:30] 🎯 Début de la détection des cartes
[2025-05-28 19:12:57] 🎯 Début de la détection des cartes
[2025-05-28 19:17:56] 🔧 Configuration de la surveillance des crashes
[2025-05-28 19:17:56] ✅ Surveillance des crashes configurée
[2025-05-28 19:17:56] 🚀 Application detector_gui.py démarrée avec surveillance des crashes
[2025-05-28 19:19:01] 🚪 Événement de fermeture de l'application détecté
[2025-05-28 19:19:01] ✅ Fermeture propre de l'application terminée
[2025-05-28 19:19:01] 🧹 Nettoyage à la sortie du programme
[2025-05-28 19:19:01] 📊 Informations système finales:
[2025-05-28 19:19:01]    CPU: 5.2%
[2025-05-28 19:19:01]    RAM: 37.5%
[2025-05-28 19:19:01] 👋 Fin du programme
[2025-05-28 19:19:12] 🔧 Configuration de la surveillance des crashes
[2025-05-28 19:19:12] ✅ Surveillance des crashes configurée
[2025-05-28 19:19:12] 🚀 Application detector_gui.py démarrée avec surveillance des crashes
[2025-05-28 19:21:21] 🎯 Début de la détection des cartes
[2025-05-28 19:24:35] 🎯 Début de la détection des cartes
[2025-05-28 19:25:00] 🎯 Début de la détection des cartes
[2025-05-28 19:25:21] 🎯 Début de la détection des cartes
[2025-05-28 19:25:37] 🎯 Début de la détection des cartes
[2025-05-28 19:27:31] 🔧 Configuration de la surveillance des crashes
[2025-05-28 19:27:31] ✅ Surveillance des crashes configurée
[2025-05-28 19:27:31] 🚀 Application detector_gui.py démarrée avec surveillance des crashes
[2025-05-28 19:28:06] 🎯 Début de la détection des cartes
[2025-05-28 19:28:21] 🎯 Début de la détection des cartes
[2025-05-28 19:29:06] 🎯 Début de la détection des cartes
[2025-05-28 19:29:19] 🔧 Configuration de la surveillance des crashes
[2025-05-28 19:29:19] ✅ Surveillance des crashes configurée
[2025-05-28 19:29:19] 🚀 Application detector_gui.py démarrée avec surveillance des crashes
[2025-05-28 19:29:48] 🎯 Début de la détection des cartes
[2025-05-28 19:30:11] 🎯 Début de la détection des cartes
[2025-05-28 19:30:30] 🎯 Début de la détection des cartes
[2025-05-28 19:31:08] 🎯 Début de la détection des cartes
[2025-05-28 19:31:41] 🎯 Début de la détection des cartes
[2025-05-28 19:32:17] 🎯 Début de la détection des cartes
[2025-05-28 19:32:31] 🎯 Début de la détection des cartes
[2025-05-28 19:35:05] 🔧 Configuration de la surveillance des crashes
[2025-05-28 19:35:05] ✅ Surveillance des crashes configurée
[2025-05-28 19:35:05] 🚀 Application detector_gui.py démarrée avec surveillance des crashes
[2025-05-28 19:38:44] 🚪 Événement de fermeture de l'application détecté
[2025-05-28 19:38:44] 🛑 Arrêt de la surveillance système
[2025-05-28 19:38:49] ⏰ Arrêt du timer de surveillance
[2025-05-28 19:38:49] ✅ Fermeture propre de l'application terminée
[2025-05-28 19:38:49] 🧹 Nettoyage à la sortie du programme
[2025-05-28 19:38:49] 📊 Informations système finales:
[2025-05-28 19:38:49]    CPU: 5.5%
[2025-05-28 19:38:49]    RAM: 39.5%
[2025-05-28 19:38:49] 👋 Fin du programme
[2025-05-28 19:40:47] 🔧 Configuration de la surveillance des crashes
[2025-05-28 19:40:47] ✅ Surveillance des crashes configurée
[2025-05-28 19:40:47] 🚀 Application detector_gui.py démarrée avec surveillance des crashes
[2025-05-28 19:41:00] 🎯 Début de la détection des cartes
[2025-05-28 19:45:57] 🔧 Configuration de la surveillance des crashes
[2025-05-28 19:45:57] ✅ Surveillance des crashes configurée
[2025-05-28 19:45:57] 🚀 Application detector_gui.py démarrée avec surveillance des crashes
[2025-05-28 19:49:36] 🚪 Événement de fermeture de l'application détecté
[2025-05-28 19:49:36] ✅ Fermeture propre de l'application terminée
[2025-05-28 19:49:36] 🧹 Nettoyage à la sortie du programme
[2025-05-28 19:49:36] 📊 Informations système finales:
[2025-05-28 19:49:36]    CPU: 4.4%
[2025-05-28 19:49:36]    RAM: 35.2%
[2025-05-28 19:49:36] 👋 Fin du programme
[2025-05-28 19:49:53] 🔧 Configuration de la surveillance des crashes
[2025-05-28 19:49:53] ✅ Surveillance des crashes configurée
[2025-05-28 19:49:53] 🚀 Application detector_gui.py démarrée avec surveillance des crashes
[2025-05-28 19:50:29] 🎯 Début de la détection des cartes
[2025-05-28 19:51:20] 🎯 Début de la détection des cartes
[2025-05-28 19:55:51] 🔧 Configuration de la surveillance des crashes
[2025-05-28 19:55:51] ✅ Surveillance des crashes configurée
[2025-05-28 19:55:51] 🚀 Application detector_gui.py démarrée avec surveillance des crashes
[2025-05-28 20:17:31] 🚪 Événement de fermeture de l'application détecté
[2025-05-28 20:17:31] ✅ Fermeture propre de l'application terminée
[2025-05-28 20:17:31] 🧹 Nettoyage à la sortie du programme
[2025-05-28 20:17:31] 📊 Informations système finales:
[2025-05-28 20:17:31]    CPU: 3.0%
[2025-05-28 20:17:31]    RAM: 39.5%
[2025-05-28 20:17:31] 👋 Fin du programme
[2025-05-28 20:19:08] 🔧 Configuration de la surveillance des crashes
[2025-05-28 20:19:08] ✅ Surveillance des crashes configurée
[2025-05-28 20:19:08] 🚀 Application detector_gui.py démarrée avec surveillance des crashes
[2025-05-28 20:20:18] 🎯 Début de la détection des cartes
[2025-05-28 20:20:43] 🎯 Début de la détection des cartes
[2025-05-28 20:21:26] 🎯 Début de la détection des cartes
[2025-05-28 20:21:58] 🎯 Début de la détection des cartes
[2025-05-28 20:27:09] 🔧 Configuration de la surveillance des crashes
[2025-05-28 20:27:09] ✅ Surveillance des crashes configurée
[2025-05-28 20:27:09] 🚀 Application detector_gui.py démarrée avec surveillance des crashes
[2025-05-28 20:28:18] 🚪 Événement de fermeture de l'application détecté
[2025-05-28 20:28:18] ✅ Fermeture propre de l'application terminée
[2025-05-28 20:28:18] 🧹 Nettoyage à la sortie du programme
[2025-05-28 20:28:18] 📊 Informations système finales:
[2025-05-28 20:28:18]    CPU: 5.8%
[2025-05-28 20:28:18]    RAM: 47.2%
[2025-05-28 20:28:18] 👋 Fin du programme
[2025-05-28 20:28:29] 🔧 Configuration de la surveillance des crashes
[2025-05-28 20:28:29] ✅ Surveillance des crashes configurée
[2025-05-28 20:28:29] 🚀 Application detector_gui.py démarrée avec surveillance des crashes
[2025-05-28 20:29:10] 🎯 Début de la détection des cartes
[2025-05-28 20:30:19] 🎯 Début de la détection des cartes
[2025-05-28 20:32:12] 🔧 Configuration de la surveillance des crashes
[2025-05-28 20:32:12] ✅ Surveillance des crashes configurée
[2025-05-28 20:32:12] 🚀 Application detector_gui.py démarrée avec surveillance des crashes
[2025-05-28 20:37:47] 🚪 Événement de fermeture de l'application détecté
[2025-05-28 20:37:47] ✅ Fermeture propre de l'application terminée
[2025-05-28 20:37:47] 🧹 Nettoyage à la sortie du programme
[2025-05-28 20:37:47] 📊 Informations système finales:
[2025-05-28 20:37:47]    CPU: 4.3%
[2025-05-28 20:37:47]    RAM: 39.9%
[2025-05-28 20:37:47] 👋 Fin du programme
[2025-05-28 20:40:49] 🔧 Configuration de la surveillance des crashes
[2025-05-28 20:40:49] ✅ Surveillance des crashes configurée
[2025-05-28 20:40:49] 🚀 Application detector_gui.py démarrée avec surveillance des crashes
[2025-05-28 20:41:01] 🎯 Début de la détection des cartes
[2025-05-28 20:41:10] 🚪 Événement de fermeture de l'application détecté
[2025-05-28 20:41:10] ✅ Fermeture propre de l'application terminée
[2025-05-28 20:41:10] 🧹 Nettoyage à la sortie du programme
[2025-05-28 20:41:10] 📊 Informations système finales:
[2025-05-28 20:41:10]    CPU: 9.6%
[2025-05-28 20:41:10]    RAM: 40.8%
[2025-05-28 20:41:10] 👋 Fin du programme
[2025-05-28 20:43:50] 🔧 Configuration de la surveillance des crashes
[2025-05-28 20:43:50] ✅ Surveillance des crashes configurée
[2025-05-28 20:43:50] 🚀 Application detector_gui.py démarrée avec surveillance des crashes
[2025-05-28 20:44:02] 🎯 Début de la détection des cartes
[2025-05-28 20:44:07] 🎯 Début de la détection des cartes
[2025-05-28 20:45:32] 🔧 Configuration de la surveillance des crashes
[2025-05-28 20:45:32] ✅ Surveillance des crashes configurée
[2025-05-28 20:45:32] 🚀 Application detector_gui.py démarrée avec surveillance des crashes
[2025-05-28 20:46:01] 🎯 Début de la détection des cartes
[2025-05-28 20:46:30] 🎯 Début de la détection des cartes
[2025-05-28 20:47:08] 🎯 Début de la détection des cartes
[2025-05-28 20:49:37] 🔧 Configuration de la surveillance des crashes
[2025-05-28 20:49:37] ✅ Surveillance des crashes configurée
[2025-05-28 20:49:37] 🚀 Application detector_gui.py démarrée avec surveillance des crashes
[2025-05-28 21:17:38] 🚪 Événement de fermeture de l'application détecté
[2025-05-28 21:17:38] ✅ Fermeture propre de l'application terminée
[2025-05-28 21:17:38] 🛑 Arrêt de la surveillance système
[2025-05-28 21:17:38] 🧹 Nettoyage à la sortie du programme
[2025-05-28 21:17:38] 📊 Informations système finales:
[2025-05-28 21:17:38]    CPU: 4.8%
[2025-05-28 21:17:38]    RAM: 43.3%
[2025-05-28 21:17:38] 👋 Fin du programme
[2025-05-28 21:17:52] 🔧 Configuration de la surveillance des crashes
[2025-05-28 21:17:52] ✅ Surveillance des crashes configurée
[2025-05-28 21:17:52] 🚀 Application detector_gui.py démarrée avec surveillance des crashes
[2025-05-28 21:18:32] 🎯 Début de la détection des cartes
[2025-05-28 21:20:16] 🎯 Début de la détection des cartes
[2025-05-28 21:23:27] 🎯 Début de la détection des cartes
[2025-05-28 21:35:14] 🚪 Événement de fermeture de l'application détecté
[2025-05-28 21:35:14] 🛑 Arrêt de la surveillance système
[2025-05-28 21:35:15] ⏰ Arrêt du timer de surveillance
[2025-05-28 21:35:15] ✅ Fermeture propre de l'application terminée
[2025-05-28 21:35:15] 🧹 Nettoyage à la sortie du programme
[2025-05-28 21:35:15] 📊 Informations système finales:
[2025-05-28 21:35:15]    CPU: 4.9%
[2025-05-28 21:35:15]    RAM: 40.4%
[2025-05-28 21:35:15] 👋 Fin du programme
[2025-05-28 21:35:33] 🔧 Configuration de la surveillance des crashes
[2025-05-28 21:35:33] ✅ Surveillance des crashes configurée
[2025-05-28 21:35:33] 🚀 Application detector_gui.py démarrée avec surveillance des crashes
[2025-05-28 21:36:04] 🎯 Début de la détection des cartes
[2025-05-28 21:42:47] 🔧 Configuration de la surveillance des crashes
[2025-05-28 21:42:47] ✅ Surveillance des crashes configurée
[2025-05-28 21:42:47] 🚀 Application detector_gui.py démarrée avec surveillance des crashes
[2025-05-28 21:43:11] 🎯 Début de la détection des cartes
[2025-05-29 01:07:46] 🔧 Configuration de la surveillance des crashes
[2025-05-29 01:07:46] ✅ Surveillance des crashes configurée
[2025-05-29 01:07:46] 🚀 Application detector_gui.py démarrée avec surveillance des crashes
[2025-05-29 01:08:12] 🎯 Début de la détection des cartes
[2025-05-29 01:14:11] 🔧 Configuration de la surveillance des crashes
[2025-05-29 01:14:11] ✅ Surveillance des crashes configurée
[2025-05-29 01:14:11] 🚀 Application detector_gui.py démarrée avec surveillance des crashes
[2025-05-29 01:14:36] 🎯 Début de la détection des cartes
[2025-05-29 01:16:37] 🎯 Début de la détection des cartes
[2025-05-29 01:17:23] 🎯 Début de la détection des cartes
[2025-05-29 01:17:56] 🎯 Début de la détection des cartes
[2025-05-29 01:19:37] 🎯 Début de la détection des cartes
[2025-05-29 01:19:57] 🎯 Début de la détection des cartes
[2025-05-29 01:20:22] 🎯 Début de la détection des cartes
[2025-05-29 01:21:35] 🎯 Début de la détection des cartes
[2025-05-29 01:22:14] 🎯 Début de la détection des cartes
[2025-05-29 01:22:30] 🎯 Début de la détection des cartes
[2025-05-29 01:22:56] 🎯 Début de la détection des cartes
[2025-05-29 01:23:58] 🎯 Début de la détection des cartes
[2025-05-29 01:24:25] 🎯 Début de la détection des cartes
[2025-05-29 01:24:51] 🎯 Début de la détection des cartes
[2025-05-29 01:25:14] 🎯 Début de la détection des cartes
[2025-05-29 01:25:46] 🎯 Début de la détection des cartes
[2025-05-29 01:26:24] 🎯 Début de la détection des cartes
[2025-05-29 01:27:00] 🎯 Début de la détection des cartes
[2025-05-29 01:27:42] 🎯 Début de la détection des cartes
[2025-05-29 01:28:13] 🎯 Début de la détection des cartes
[2025-05-29 01:29:09] 🎯 Début de la détection des cartes
[2025-05-29 01:29:52] 🎯 Début de la détection des cartes
[2025-05-29 01:30:39] 🎯 Début de la détection des cartes
[2025-05-29 01:32:14] 🎯 Début de la détection des cartes
[2025-05-29 01:33:33] 🎯 Début de la détection des cartes
[2025-05-29 01:33:52] 🎯 Début de la détection des cartes
[2025-05-29 01:34:27] 🎯 Début de la détection des cartes
[2025-05-29 01:34:51] 🎯 Début de la détection des cartes
[2025-05-29 01:35:45] 🎯 Début de la détection des cartes
[2025-05-29 01:36:08] 🎯 Début de la détection des cartes
[2025-05-29 01:36:45] 🎯 Début de la détection des cartes
[2025-05-29 01:37:41] 🎯 Début de la détection des cartes
[2025-05-29 01:39:01] 🎯 Début de la détection des cartes
[2025-05-29 01:39:36] 🎯 Début de la détection des cartes
[2025-05-29 01:40:01] 🎯 Début de la détection des cartes
[2025-05-29 01:40:25] 🎯 Début de la détection des cartes
[2025-05-29 01:40:54] 🎯 Début de la détection des cartes
[2025-05-29 01:41:39] 🎯 Début de la détection des cartes
[2025-05-29 01:42:50] 🎯 Début de la détection des cartes
[2025-05-29 01:43:25] 🎯 Début de la détection des cartes
[2025-05-29 01:44:49] 🎯 Début de la détection des cartes
[2025-05-29 01:45:31] 🎯 Début de la détection des cartes
[2025-05-29 01:46:16] 🎯 Début de la détection des cartes
[2025-05-29 01:46:36] 🎯 Début de la détection des cartes
[2025-05-29 01:48:52] 🎯 Début de la détection des cartes
[2025-05-29 01:49:23] 🎯 Début de la détection des cartes
[2025-05-29 01:49:41] 🎯 Début de la détection des cartes
[2025-05-29 01:50:16] 🎯 Début de la détection des cartes
[2025-05-29 01:50:34] 🎯 Début de la détection des cartes
[2025-05-29 01:50:58] 🎯 Début de la détection des cartes
[2025-05-29 01:52:01] 🎯 Début de la détection des cartes
[2025-05-29 01:53:55] 🎯 Début de la détection des cartes
[2025-05-29 01:54:53] 🎯 Début de la détection des cartes
[2025-05-29 02:01:02] 🎯 Début de la détection des cartes
[2025-05-29 02:02:10] 🎯 Début de la détection des cartes
[2025-05-29 02:02:51] 🎯 Début de la détection des cartes
[2025-05-29 02:04:19] 🎯 Début de la détection des cartes
[2025-05-29 02:04:42] 🎯 Début de la détection des cartes
[2025-05-29 02:05:01] 🎯 Début de la détection des cartes
[2025-05-29 02:05:12] 🎯 Début de la détection des cartes
[2025-05-29 02:05:32] 🎯 Début de la détection des cartes
[2025-05-29 02:06:16] 🎯 Début de la détection des cartes
[2025-05-29 02:06:42] 🎯 Début de la détection des cartes
[2025-05-29 02:07:07] 🎯 Début de la détection des cartes
[2025-05-29 02:07:43] 🎯 Début de la détection des cartes
[2025-05-29 02:08:39] 🎯 Début de la détection des cartes
[2025-05-29 02:09:29] 🎯 Début de la détection des cartes
[2025-05-29 02:10:40] 🎯 Début de la détection des cartes
[2025-05-29 02:11:47] 🎯 Début de la détection des cartes
[2025-05-29 02:13:17] 🎯 Début de la détection des cartes
[2025-05-29 02:13:52] 🎯 Début de la détection des cartes
[2025-05-29 02:14:21] 🎯 Début de la détection des cartes
[2025-05-29 02:14:43] 🎯 Début de la détection des cartes
[2025-05-29 02:15:42] 🎯 Début de la détection des cartes
[2025-05-29 02:16:20] 🎯 Début de la détection des cartes
[2025-05-29 02:16:41] 🎯 Début de la détection des cartes
[2025-05-29 02:17:06] 🎯 Début de la détection des cartes
[2025-05-29 02:18:25] 🎯 Début de la détection des cartes
[2025-05-29 02:18:50] 🎯 Début de la détection des cartes
[2025-05-29 02:19:13] 🎯 Début de la détection des cartes
[2025-05-29 02:20:17] 🎯 Début de la détection des cartes
[2025-05-29 02:20:39] 🎯 Début de la détection des cartes
[2025-05-29 02:21:05] 🎯 Début de la détection des cartes
[2025-05-29 02:21:49] 🎯 Début de la détection des cartes
[2025-05-29 02:22:39] 🎯 Début de la détection des cartes
[2025-05-29 02:23:03] 🎯 Début de la détection des cartes
[2025-05-29 02:23:31] 🎯 Début de la détection des cartes
[2025-05-29 02:23:58] 🎯 Début de la détection des cartes
[2025-05-29 02:24:59] 🎯 Début de la détection des cartes
[2025-05-29 02:25:17] 🎯 Début de la détection des cartes
[2025-05-29 02:25:51] 🎯 Début de la détection des cartes
[2025-05-29 02:26:19] 🎯 Début de la détection des cartes
[2025-05-29 02:26:34] 🎯 Début de la détection des cartes
[2025-05-29 02:26:55] 🎯 Début de la détection des cartes
[2025-05-29 02:28:14] 🎯 Début de la détection des cartes
[2025-05-29 02:28:44] 🎯 Début de la détection des cartes
[2025-05-29 02:29:14] 🎯 Début de la détection des cartes
[2025-05-29 02:29:43] 🎯 Début de la détection des cartes
[2025-05-29 02:30:13] 🎯 Début de la détection des cartes
[2025-05-29 02:30:30] 🎯 Début de la détection des cartes
[2025-05-29 02:30:59] 🎯 Début de la détection des cartes
[2025-05-29 02:31:19] 🎯 Début de la détection des cartes
[2025-05-29 02:32:21] 🎯 Début de la détection des cartes
[2025-05-29 02:34:05] 🎯 Début de la détection des cartes
[2025-05-29 02:34:40] 🎯 Début de la détection des cartes
[2025-05-29 02:40:08] 🎯 Début de la détection des cartes
[2025-05-29 02:41:05] 🎯 Début de la détection des cartes
[2025-05-29 02:41:42] 🎯 Début de la détection des cartes
[2025-05-29 02:42:11] 🎯 Début de la détection des cartes
[2025-05-29 02:43:02] 🎯 Début de la détection des cartes
[2025-05-29 02:44:47] 🎯 Début de la détection des cartes
[2025-05-29 02:45:20] 🎯 Début de la détection des cartes
[2025-05-29 02:45:45] 🎯 Début de la détection des cartes
[2025-05-29 02:46:37] 🎯 Début de la détection des cartes
[2025-05-29 02:47:53] 🎯 Début de la détection des cartes
[2025-05-29 02:48:42] 🎯 Début de la détection des cartes
[2025-05-29 02:49:12] 🎯 Début de la détection des cartes
[2025-05-29 02:49:28] 🎯 Début de la détection des cartes
[2025-05-29 02:50:02] 🎯 Début de la détection des cartes
[2025-05-29 02:50:46] 🎯 Début de la détection des cartes
[2025-05-29 02:51:59] 🎯 Début de la détection des cartes
[2025-05-29 02:52:32] 🎯 Début de la détection des cartes
[2025-05-29 02:52:57] 🎯 Début de la détection des cartes
[2025-05-29 03:17:31] 🔧 Configuration de la surveillance des crashes
[2025-05-29 03:17:31] ✅ Surveillance des crashes configurée
[2025-05-29 03:17:31] 🚀 Application detector_gui.py démarrée avec surveillance des crashes
[2025-05-29 03:17:54] 🎯 Début de la détection des cartes
[2025-05-29 03:18:13] 🎯 Début de la détection des cartes
[2025-05-29 03:18:42] 🎯 Début de la détection des cartes
