#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test de l'optimisation RTX 3060 Ti 6GB + 32GB RAM
"""

import time
import cv2
import numpy as np

def test_detector_persistence():
    """Test de la persistance du détecteur"""
    print("🚀 TEST OPTIMISATION RTX 3060 Ti 6GB + 32GB RAM")
    print("=" * 60)
    
    try:
        from detector import Detector
        
        # Créer une image de test
        test_image = np.ones((200, 300, 3), dtype=np.uint8) * 255
        cv2.putText(test_image, "A", (100, 120), cv2.FONT_HERSHEY_SIMPLEX, 4, (0, 0, 0), 6)
        
        print("🔄 Test 1: Première initialisation (devrait prendre 6-8 secondes)")
        start_time = time.time()
        
        detector = Detector(use_cuda=True)
        
        init_time = time.time() - start_time
        print(f"✅ Initialisation: {init_time:.2f}s")
        
        # Test de détection
        print("🔄 Test 2: Première détection")
        start_time = time.time()
        result1 = detector.detect_text_fast(test_image)
        detection_time1 = time.time() - start_time
        print(f"✅ Première détection: {detection_time1:.3f}s - Résultat: '{result1}'")
        
        # Test de détections suivantes (devraient être rapides)
        print("🔄 Test 3: Détections suivantes (devraient être rapides)")
        
        times = []
        for i in range(5):
            start_time = time.time()
            result = detector.detect_text_fast(test_image)
            detection_time = time.time() - start_time
            times.append(detection_time)
            print(f"  Détection {i+2}: {detection_time:.3f}s - Résultat: '{result}'")
        
        avg_time = sum(times) / len(times)
        print(f"✅ Temps moyen détections 2-6: {avg_time:.3f}s")
        
        # Analyse des performances
        print("\n📊 ANALYSE DES PERFORMANCES:")
        print(f"  Initialisation: {init_time:.2f}s")
        print(f"  Première détection: {detection_time1:.3f}s")
        print(f"  Détections suivantes: {avg_time:.3f}s")
        
        if avg_time < 0.5:
            print("🎉 EXCELLENT: Détections ultra-rapides !")
        elif avg_time < 1.0:
            print("✅ BON: Détections rapides")
        else:
            print("⚠️ LENT: Optimisation nécessaire")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_memory_usage():
    """Test de l'utilisation mémoire"""
    print("\n🧠 TEST UTILISATION MÉMOIRE")
    print("=" * 40)
    
    try:
        import psutil
        import torch
        
        # Mémoire avant
        process = psutil.Process()
        ram_before = process.memory_info().rss / 1024 / 1024  # MB
        
        if torch.cuda.is_available():
            vram_before = torch.cuda.memory_allocated() / 1024 / 1024  # MB
            print(f"📊 Avant - RAM: {ram_before:.1f}MB, VRAM: {vram_before:.1f}MB")
        else:
            print(f"📊 Avant - RAM: {ram_before:.1f}MB, VRAM: N/A")
        
        # Créer le détecteur
        from detector import Detector
        detector = Detector(use_cuda=True)
        
        # Mémoire après
        ram_after = process.memory_info().rss / 1024 / 1024  # MB
        
        if torch.cuda.is_available():
            vram_after = torch.cuda.memory_allocated() / 1024 / 1024  # MB
            print(f"📊 Après - RAM: {ram_after:.1f}MB, VRAM: {vram_after:.1f}MB")
            
            ram_diff = ram_after - ram_before
            vram_diff = vram_after - vram_before
            
            print(f"📈 Différence - RAM: +{ram_diff:.1f}MB, VRAM: +{vram_diff:.1f}MB")
            
            # Vérifications
            if vram_diff < 4000:  # Moins de 4GB
                print("✅ Utilisation VRAM acceptable pour RTX 3060 Ti 6GB")
            else:
                print("⚠️ Utilisation VRAM élevée")
                
            if ram_diff < 2000:  # Moins de 2GB
                print("✅ Utilisation RAM acceptable pour 32GB")
            else:
                print("⚠️ Utilisation RAM élevée")
        
        return True
        
    except ImportError:
        print("⚠️ psutil non disponible, impossible de tester la mémoire")
        return False
    except Exception as e:
        print(f"❌ Erreur test mémoire: {e}")
        return False

def main():
    print("🔥 TEST COMPLET OPTIMISATION RTX 3060 Ti")
    print("=" * 60)
    
    # Test 1: Persistance du détecteur
    persistence_ok = test_detector_persistence()
    
    # Test 2: Utilisation mémoire
    memory_ok = test_memory_usage()
    
    # Résumé
    print("\n" + "=" * 60)
    print("📋 RÉSUMÉ DES TESTS")
    print("=" * 60)
    print(f"Persistance détecteur: {'✅ OK' if persistence_ok else '❌ ÉCHEC'}")
    print(f"Utilisation mémoire: {'✅ OK' if memory_ok else '❌ ÉCHEC'}")
    
    if persistence_ok and memory_ok:
        print("\n🎉 OPTIMISATION RÉUSSIE !")
        print("💡 Votre application devrait maintenant avoir :")
        print("   - Initialisation: 6-8s (première fois)")
        print("   - Détections: <0.5s (fois suivantes)")
        print("   - Mémoire: Optimisée pour RTX 3060 Ti 6GB")
    elif persistence_ok:
        print("\n⚠️ Optimisation partielle")
        print("💡 La persistance fonctionne mais vérifiez la mémoire")
    else:
        print("\n❌ Optimisation échouée")
        print("💡 Vérifiez l'installation de PaddleOCR et CUDA")

if __name__ == "__main__":
    main()
