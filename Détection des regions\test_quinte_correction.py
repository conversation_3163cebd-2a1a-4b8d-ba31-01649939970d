#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Script de test pour vérifier la correction de la détection des quintes.
"""

import sys
import os

def test_straight_detection():
    """Test de la détection des quintes corrigée"""
    print("🔍 Test de la détection des quintes corrigée")
    print("=" * 60)

    try:
        # Importer la classe DetectorGUI
        from detector_gui import DetectorGUI
        from PyQt5.QtWidgets import QApplication

        # Créer une application Qt (nécessaire pour DetectorGUI)
        app = QApplication(sys.argv)

        # Créer une instance de DetectorGUI
        detector_gui = DetectorGUI()

        print("✅ DetectorGUI créé avec succès")

        # Test 1: Situation où une quinte n'est PAS possible (toutes les cartes sorties)
        print("\n🧪 Test 1: Quinte impossible (cartes déjà sorties)")
        hand_values = ["A", "K"]
        hand_suits = ["Cœur", "Pique"]
        board_values = ["Q", "J", "10", "9", "8"]  # Toutes les cartes de la quinte A-10 sont sorties
        board_suits = ["Trèfle", "Carreau", "Cœur", "Pique", "Trèfle"]

        straight_possible, straight_desc = detector_gui.check_straight_possibility(
            hand_values, hand_suits, board_values, board_suits
        )

        # Debug: Afficher toutes les cartes
        all_values = hand_values + board_values
        numeric_values = [detector_gui.card_to_numeric(v) for v in all_values]
        unique_values = sorted(set(numeric_values), reverse=True)

        print(f"   Cartes en main: {hand_values} de {hand_suits}")
        print(f"   Cartes du board: {board_values} de {board_suits}")
        print(f"   Toutes les valeurs: {all_values}")
        print(f"   Valeurs numériques uniques: {unique_values}")
        print(f"   Quinte possible: {straight_possible}")
        print(f"   Description: {straight_desc}")

        if not straight_possible:
            print("   ✅ Correct: Aucune quinte possible détectée")
        else:
            print("   ❌ Erreur: Quinte détectée alors qu'elle n'est pas possible")

        # Test 2: Situation où une quinte EST possible
        print("\n🧪 Test 2: Quinte possible (tirage ouvert)")
        hand_values = ["A", "K"]
        hand_suits = ["Cœur", "Pique"]
        board_values = ["Q", "J", "7"]  # Besoin d'un 10 pour la quinte
        board_suits = ["Trèfle", "Carreau", "Cœur"]

        straight_possible, straight_desc = detector_gui.check_straight_possibility(
            hand_values, hand_suits, board_values, board_suits
        )

        print(f"   Cartes en main: {hand_values} de {hand_suits}")
        print(f"   Cartes du board: {board_values} de {board_suits}")
        print(f"   Quinte possible: {straight_possible}")
        print(f"   Description: {straight_desc}")

        if straight_possible and "4 outs" in straight_desc:
            print("   ✅ Correct: Quinte possible détectée avec 4 outs")
        else:
            print("   ❌ Erreur: Détection incorrecte")

        # Test 3: Situation où une quinte n'est PAS possible (pas assez de cartes)
        print("\n🧪 Test 3: Quinte impossible (pas assez de cartes connectées)")
        hand_values = ["A", "7"]
        hand_suits = ["Cœur", "Pique"]
        board_values = ["K", "3", "2"]  # Cartes non connectées
        board_suits = ["Trèfle", "Carreau", "Cœur"]

        straight_possible, straight_desc = detector_gui.check_straight_possibility(
            hand_values, hand_suits, board_values, board_suits
        )

        print(f"   Cartes en main: {hand_values} de {hand_suits}")
        print(f"   Cartes du board: {board_values} de {board_suits}")
        print(f"   Quinte possible: {straight_possible}")
        print(f"   Description: {straight_desc}")

        if not straight_possible:
            print("   ✅ Correct: Aucune quinte possible détectée")
        else:
            print("   ❌ Erreur: Quinte détectée alors qu'elle n'est pas possible")

        # Test 4: Quinte blanche complète (A-5-4-3-2)
        print("\n🧪 Test 4: Quinte blanche complète")
        hand_values = ["A", "2"]
        hand_suits = ["Cœur", "Pique"]
        board_values = ["5", "4", "3"]  # Quinte blanche complète A-5-4-3-2
        board_suits = ["Trèfle", "Carreau", "Cœur"]

        straight_possible, straight_desc = detector_gui.check_straight_possibility(
            hand_values, hand_suits, board_values, board_suits
        )

        print(f"   Cartes en main: {hand_values} de {hand_suits}")
        print(f"   Cartes du board: {board_values} de {board_suits}")
        print(f"   Quinte possible: {straight_possible}")
        print(f"   Description: {straight_desc}")

        if not straight_possible and "déjà formée" in straight_desc:
            print("   ✅ Correct: Quinte blanche complète détectée (pas de tirage nécessaire)")
        else:
            print("   ❌ Erreur: Devrait détecter une quinte déjà formée")

        # Test 5: Quinte complète (9-8-7-6-5)
        print("\n🧪 Test 5: Quinte complète")
        hand_values = ["9", "8"]
        hand_suits = ["Cœur", "Pique"]
        board_values = ["7", "6", "J", "Q", "5"]  # Quinte 9-8-7-6-5 complète
        board_suits = ["Trèfle", "Carreau", "Cœur", "Pique", "Trèfle"]

        straight_possible, straight_desc = detector_gui.check_straight_possibility(
            hand_values, hand_suits, board_values, board_suits
        )

        print(f"   Cartes en main: {hand_values} de {hand_suits}")
        print(f"   Cartes du board: {board_values} de {board_suits}")
        print(f"   Quinte possible: {straight_possible}")
        print(f"   Description: {straight_desc}")

        if not straight_possible and "déjà formée" in straight_desc:
            print("   ✅ Correct: Quinte complète détectée (pas de tirage nécessaire)")
        else:
            print("   ❌ Erreur: Devrait détecter une quinte déjà formée")

        print("\n🎉 Tests de détection des quintes terminés")

        # Fermer l'application
        app.quit()

        return True

    except Exception as e:
        print(f"❌ Erreur lors du test: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Fonction principale de test"""
    print("🚀 TESTS DE CORRECTION DE LA DÉTECTION DES QUINTES")
    print("=" * 60)

    if test_straight_detection():
        print("\n✅ TOUS LES TESTS SONT RÉUSSIS!")
        print("La détection des quintes a été corrigée avec succès.")
    else:
        print("\n❌ CERTAINS TESTS ONT ÉCHOUÉ")
        print("Il reste des problèmes dans la détection des quintes.")

    print("\n👋 Tests terminés")

if __name__ == "__main__":
    main()
