#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test de la capture unique avec détection de montants
===================================================

Ce script teste que la fonction "Capturer l'écran" utilise bien
la nouvelle détection de montants au lieu de l'ancienne méthode.

Auteur: Augment Agent
Date: 2025-01-27
"""

import os
import sys
import cv2
import numpy as np
from detector import Detector

def test_capture_unique_montants():
    """Teste la capture unique avec détection de montants"""
    print("🧪 TEST DE LA CAPTURE UNIQUE AVEC MONTANTS")
    print("=" * 60)
    
    # Charger la configuration
    config_path = os.path.abspath(os.path.join('..', 'Calibration', 'config', 'poker_advisor_config.json'))
    
    if not os.path.exists(config_path):
        print(f"❌ Configuration non trouvée: {config_path}")
        return False
    
    # Initialiser le détecteur
    try:
        detector = Detector(config_path, use_cuda=True)
        print("✅ Détecteur initialisé")
    except Exception as e:
        print(f"❌ Erreur lors de l'initialisation: {e}")
        return False
    
    # Créer une image de test avec des montants
    print("\n🔍 Création d'une image de test avec montants...")
    test_image = np.zeros((1440, 2560, 3), dtype=np.uint8)
    test_image[:] = (50, 50, 50)  # Fond gris foncé
    
    # Ajouter des montants simulés
    test_amounts = [
        ("58,9 BB", (100, 100)),
        ("22,8 BB", (300, 200)),
        ("5,2 BB", (500, 300)),
        ("45,3 BB", (700, 400)),
        ("10,5 BB", (900, 500))
    ]
    
    for amount_text, (x, y) in test_amounts:
        cv2.putText(test_image, amount_text, (x, y), 
                   cv2.FONT_HERSHEY_SIMPLEX, 1.5, (255, 255, 255), 2)
        print(f"   Ajouté '{amount_text}' à la position {x}, {y}")
    
    # Sauvegarder l'image temporaire
    temp_image_path = "test_capture_unique.jpg"
    cv2.imwrite(temp_image_path, test_image)
    print(f"✅ Image de test sauvegardée: {temp_image_path}")
    
    # Test 1: Ancienne méthode process_image (pour comparaison)
    print("\n🔍 TEST 1: Ancienne méthode process_image")
    try:
        results_old = detector.process_image(temp_image_path)
        if results_old:
            print(f"✅ Ancienne méthode: {len(results_old)} régions traitées")
            
            # Compter les montants détectés
            montants_detectes_old = 0
            for region_name, data in results_old.items():
                is_amount_region = (region_name == "mes_jetons" or region_name.startswith("jetons_joueur") or 
                                  region_name == "ma_mise" or region_name.startswith("mise_joueur") or
                                  region_name in ["pot", "pot_total"])
                
                if is_amount_region:
                    detected_text = data.get('text', '')
                    if detected_text and detected_text.strip():
                        montants_detectes_old += 1
                        print(f"   {region_name}: '{detected_text}'")
            
            print(f"   Montants détectés avec ancienne méthode: {montants_detectes_old}")
        else:
            print("❌ Ancienne méthode a échoué")
    except Exception as e:
        print(f"❌ Erreur avec ancienne méthode: {e}")
    
    # Test 2: Nouvelle méthode process_image_direct (comme dans la capture unique corrigée)
    print("\n🔍 TEST 2: Nouvelle méthode process_image_direct")
    try:
        results_new = detector.process_image_direct(test_image, fast_mode=True, parallel=False)
        if results_new:
            print(f"✅ Nouvelle méthode: {len(results_new)} régions traitées")
            
            # Compter les montants détectés
            montants_detectes_new = 0
            for region_name, data in results_new.items():
                is_amount_region = (region_name == "mes_jetons" or region_name.startswith("jetons_joueur") or 
                                  region_name == "ma_mise" or region_name.startswith("mise_joueur") or
                                  region_name in ["pot", "pot_total"])
                
                if is_amount_region:
                    detected_text = data.get('text', '')
                    if detected_text and detected_text.strip():
                        montants_detectes_new += 1
                        print(f"   {region_name}: '{detected_text}' ✅")
                    else:
                        print(f"   {region_name}: Aucun montant détecté")
            
            print(f"   Montants détectés avec nouvelle méthode: {montants_detectes_new}")
        else:
            print("❌ Nouvelle méthode a échoué")
    except Exception as e:
        print(f"❌ Erreur avec nouvelle méthode: {e}")
        import traceback
        print(f"❌ Trace: {traceback.format_exc()}")
    
    # Test 3: Simulation de la capture unique corrigée
    print("\n🔍 TEST 3: Simulation de la capture unique corrigée")
    try:
        # Simuler exactement ce que fait DetectionThread.run() maintenant
        image = cv2.imread(temp_image_path)
        if image is None:
            print("❌ Impossible de charger l'image de test")
        else:
            print("✅ Image chargée avec OpenCV")
            
            # Utiliser process_image_direct comme dans la correction
            results_corrected = detector.process_image_direct(image, fast_mode=True, parallel=False)
            
            if results_corrected:
                print(f"✅ Capture unique corrigée: {len(results_corrected)} régions traitées")
                
                # Vérifier spécifiquement les régions de montants
                print("\n📊 RÉSULTATS DÉTAILLÉS:")
                for region_name, data in results_corrected.items():
                    is_amount_region = (region_name == "mes_jetons" or region_name.startswith("jetons_joueur") or 
                                      region_name == "ma_mise" or region_name.startswith("mise_joueur") or
                                      region_name in ["pot", "pot_total"])
                    
                    if is_amount_region:
                        detected_text = data.get('text', '')
                        colors = data.get('colors', [])
                        
                        if detected_text and detected_text.strip():
                            print(f"   💰 {region_name}: '{detected_text}' (couleurs: {colors}) ✅")
                        else:
                            print(f"   💰 {region_name}: Aucun montant détecté")
                
                print("\n✅ LA CAPTURE UNIQUE UTILISE MAINTENANT LA DÉTECTION DE MONTANTS!")
                return True
            else:
                print("❌ Capture unique corrigée a échoué")
                return False
    except Exception as e:
        print(f"❌ Erreur avec capture unique corrigée: {e}")
        return False
    
    finally:
        # Nettoyer le fichier temporaire
        try:
            if os.path.exists(temp_image_path):
                os.remove(temp_image_path)
                print(f"🧹 Fichier temporaire supprimé: {temp_image_path}")
        except:
            pass

def test_detection_thread_simulation():
    """Teste la simulation exacte du DetectionThread corrigé"""
    print("\n🧪 TEST DE SIMULATION DU DETECTIONTHREAD")
    print("=" * 60)
    
    # Charger la configuration
    config_path = os.path.abspath(os.path.join('..', 'Calibration', 'config', 'poker_advisor_config.json'))
    
    try:
        detector = Detector(config_path, use_cuda=True)
        print("✅ Détecteur initialisé pour simulation")
    except Exception as e:
        print(f"❌ Erreur lors de l'initialisation: {e}")
        return False
    
    # Créer une image simple avec un montant
    test_image = np.zeros((200, 400, 3), dtype=np.uint8)
    test_image[:] = (50, 50, 50)
    cv2.putText(test_image, "42,8 BB", (50, 100), cv2.FONT_HERSHEY_SIMPLEX, 2, (255, 255, 255), 3)
    
    # Sauvegarder temporairement
    temp_path = "simulation_test.jpg"
    cv2.imwrite(temp_path, test_image)
    
    try:
        # Simuler exactement DetectionThread.run()
        print("🔍 Simulation de DetectionThread.run()...")
        
        # Vérifier si le fichier existe
        if not os.path.exists(temp_path):
            print("❌ Le fichier n'existe pas")
            return False
        
        # Charger l'image avec OpenCV
        image = cv2.imread(temp_path)
        if image is None:
            print("❌ Impossible de charger l'image")
            return False
        
        print("✅ Image chargée avec OpenCV")
        
        # Traiter l'image avec la nouvelle méthode
        results = detector.process_image_direct(image, fast_mode=True, parallel=False)
        
        if results:
            print(f"✅ Détection réussie: {len(results)} régions")
            
            # Chercher des montants détectés
            montants_trouves = 0
            for region_name, data in results.items():
                detected_text = data.get('text', '')
                if detected_text and '42' in detected_text:
                    montants_trouves += 1
                    print(f"   💰 Montant trouvé dans {region_name}: '{detected_text}'")
            
            if montants_trouves > 0:
                print("✅ SIMULATION RÉUSSIE: Les montants sont détectés!")
                return True
            else:
                print("⚠️ Aucun montant spécifique détecté, mais la méthode fonctionne")
                return True
        else:
            print("❌ Aucun résultat de détection")
            return False
            
    except Exception as e:
        print(f"❌ Erreur lors de la simulation: {e}")
        return False
    finally:
        # Nettoyer
        try:
            if os.path.exists(temp_path):
                os.remove(temp_path)
        except:
            pass

if __name__ == "__main__":
    print("🚀 DÉMARRAGE DES TESTS DE CAPTURE UNIQUE")
    print()
    
    success1 = test_capture_unique_montants()
    success2 = test_detection_thread_simulation()
    
    print("\n" + "=" * 60)
    if success1 and success2:
        print("🎉 TOUS LES TESTS RÉUSSIS!")
        print("La capture unique utilise maintenant la détection de montants.")
        print("Vous devriez voir '💰 Montant: 58,9' au lieu de '💰 Montant: Aucun détecté'")
    else:
        print("❌ CERTAINS TESTS ONT ÉCHOUÉ")
        print("Il peut y avoir encore des problèmes.")
    
    input("\nAppuyez sur Entrée pour fermer...")
