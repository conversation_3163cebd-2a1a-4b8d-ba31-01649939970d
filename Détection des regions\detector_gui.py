#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Poker Advisor - Interface graphique du module de détection
=========================================================

Interface graphique pour le module de détection de cartes et de couleurs.
Utilise PyQt5 pour l'interface et intègre le module detector.py.

Cette application permet de :
1. Charger une image ou capturer l'écran
2. Sélectionner les régions à analyser
3. Détecter le texte et les couleurs dans ces régions
4. Visualiser et sauvegarder les résultats
5. Charger différentes configurations de régions
6. Analyser les cartes détectées avec le conseiller poker intégré

Auteur: Augment Agent
Date: 2023-2025
"""

import os
import sys
import json
import time
import random
import numpy as np
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                            QPushButton, QLabel, QFileDialog, QCheckBox,
                            QGroupBox, QGridLayout, QScrollArea, QSplitter, QFrame,
                            QMessageBox, QStatusBar, QAction, QToolBar, QSpinBox, QDoubleSpinBox,
                            QTextEdit)
from PyQt5.QtGui import QPixmap, QImage, QDragEnterEvent, QDropEvent, QColor, QTextCharFormat
from PyQt5.QtCore import Qt, QSize, QThread, pyqtSignal, QTimer

import cv2
from detector import Detector

# Importer le conseiller poker (si disponible)
try:
    from poker_advisor_light import PokerAdvisorLight
    POKER_ADVISOR_AVAILABLE = True
    print("✅ Module poker_advisor_light importé avec succès")
except ImportError:
    POKER_ADVISOR_AVAILABLE = False
    print("⚠️ Module poker_advisor_light non disponible")

# Importer le système d'apprentissage (si disponible)
try:
    from learning_system import LearningSystem
    LEARNING_SYSTEM_AVAILABLE = True
    print("✅ Module learning_system importé avec succès")
except ImportError:
    LEARNING_SYSTEM_AVAILABLE = False
    print("⚠️ Module learning_system non disponible")

# Importer le système de surveillance (si disponible)
try:
    from monitor_app import AppMonitor
    MONITORING_AVAILABLE = True
    print("✅ Module monitor_app importé avec succès")
except ImportError:
    MONITORING_AVAILABLE = False
    print("⚠️ Module monitor_app non disponible")

# Importer le système de debug des crashes (si disponible)
try:
    from debug_crash import setup_crash_monitoring, log_debug
    DEBUG_CRASH_AVAILABLE = True
    print("✅ Module debug_crash importé avec succès")
    # Configurer automatiquement la surveillance des crashes
    setup_crash_monitoring()
    log_debug("🚀 Application detector_gui.py démarrée avec surveillance des crashes")
except ImportError:
    DEBUG_CRASH_AVAILABLE = False
    print("⚠️ Module debug_crash non disponible")

# Importer la logique avancée de poker (OBLIGATOIRE)
# ANCIENNE LOGIQUE SUPPRIMÉE - SEULE LA LOGIQUE AVANCÉE EST UTILISÉE
try:
    from poker_advisor_integration import poker_integration
    ADVANCED_POKER_LOGIC_AVAILABLE = True
    print("✅ Logique avancée de poker importée avec succès")
except ImportError as e:
    print(f"❌ ERREUR CRITIQUE: Impossible d'importer la logique avancée: {e}")
    print("❌ L'application ne peut pas fonctionner sans la logique avancée")
    ADVANCED_POKER_LOGIC_AVAILABLE = False
    raise ImportError("La logique avancée de poker est obligatoire pour le fonctionnement de l'application")

# Importation pour la capture d'écran (comme dans calibration_simple.py)
import mss
import mss.tools
SCREEN_CAPTURE_AVAILABLE = True
print("✅ Module mss importé avec succès")

# Fonction de capture d'écran globale (identique à celle de calibration_simple.py)
def capture_screen(region=None):
    """Capture l'écran ou une région spécifique (identique à calibration_simple.py)"""
    try:
        print("🔍 Tentative de capture d'écran avec mss...")

        with mss.mss() as sct:
            # Si aucune région n'est spécifiée, capturer tout l'écran
            if region is None:
                region = sct.monitors[1]  # Le moniteur principal
                print(f"🔍 Capture du moniteur principal: {region}")

            # Capturer l'écran
            sct_img = sct.grab(region)

            # Convertir en tableau numpy
            img = np.array(sct_img)

            # Convertir de BGRA à BGR (supprimer le canal alpha)
            img = cv2.cvtColor(img, cv2.COLOR_BGRA2BGR)

            print(f"✅ Capture d'écran réussie: {img.shape}")
            return img

    except Exception as e:
        import traceback
        print(f"❌ Erreur lors de la capture d'écran: {e}")
        print(f"❌ Détails de l'erreur: {traceback.format_exc()}")

        # En cas d'erreur, retourner une image noire de taille 1920x1080
        print("⚠️ Retour d'une image noire de secours")
        return np.zeros((1080, 1920, 3), dtype=np.uint8)

# Constantes
CARD_SUITS = {
    'hearts': '♥',   # Cœur
    'clubs': '♣',    # Trèfle
    'diamonds': '♦', # Carreau
    'spades': '♠'    # Pique
}

SUIT_COLORS = {
    'hearts': 'red',
    'clubs': 'green',
    'diamonds': 'blue',
    'spades': 'black'
}

class DetectionThread(QThread):
    """Thread pour exécuter la détection en arrière-plan

    Cette classe permet d'exécuter la détection dans un thread séparé
    pour éviter de bloquer l'interface utilisateur pendant le traitement.

    Signals:
        finished (dict): Émis lorsque la détection est terminée, avec les résultats
        progress (str): Émis pour indiquer la progression du traitement
        error (str): Émis en cas d'erreur
    """
    finished = pyqtSignal(dict)
    progress = pyqtSignal(str)
    error = pyqtSignal(str)

    def __init__(self, detector, image_path):
        """Initialise le thread de détection

        Args:
            detector (Detector): Instance du détecteur à utiliser
            image_path (str): Chemin vers l'image à analyser
        """
        super().__init__()
        self.detector = detector
        self.image_path = image_path

    def run(self):
        """Exécute la détection dans un thread séparé"""
        try:
            self.progress.emit("Chargement de l'image...")

            # Vérifier si le fichier existe
            if not os.path.exists(self.image_path):
                self.error.emit(f"Le fichier n'existe pas: {self.image_path}")
                return

            # Traiter l'image
            self.progress.emit("Détection en cours...")
            results = self.detector.process_image(self.image_path)

            # Émettre le signal de fin avec les résultats
            self.finished.emit(results)

        except Exception as e:
            self.error.emit(f"Erreur lors de la détection: {str(e)}")


class ScreenCaptureThread(QThread):
    """Thread pour capturer l'écran en temps réel et exécuter la détection

    Cette classe permet de capturer l'écran à intervalles réguliers et d'exécuter
    la détection sur chaque capture, le tout dans un thread séparé pour éviter
    de bloquer l'interface utilisateur.

    Signals:
        image_captured (object): Émis lorsqu'une image est capturée, avec l'image
        detection_finished (dict): Émis lorsque la détection est terminée, avec les résultats
        progress (str): Émis pour indiquer la progression du traitement
        error (str): Émis en cas d'erreur
    """
    image_captured = pyqtSignal(object)  # Signal émis lorsqu'une image est capturée
    detection_finished = pyqtSignal(dict)  # Signal émis lorsque la détection est terminée
    progress = pyqtSignal(str)
    error = pyqtSignal(str)

    def __init__(self, detector, interval=1.0, fast_mode=True):
        """Initialise le thread de capture d'écran

        Args:
            detector (Detector): Instance du détecteur à utiliser
            interval (float, optional): Intervalle entre les captures en secondes.
                Defaults to 1.0.
            fast_mode (bool, optional): Utiliser le mode rapide. Defaults to True.
        """
        super().__init__()
        self.detector = detector
        self.interval = interval  # Intervalle entre les captures en secondes
        self.fast_mode = fast_mode  # Mode rapide pour améliorer les performances
        self.running = False
        self.paused = False
        self.temp_path = "temp_screenshot.jpg"
        self.consecutive_errors = 0  # Compteur d'erreurs consécutives
        self.max_consecutive_errors = 5  # Limite d'erreurs avant arrêt
        self.detection_count = 0  # Compteur de détections
        self.cleanup_interval = 10  # Nettoyage tous les 10 détections

    def run(self):
        """Capture l'écran à intervalles réguliers et exécute la détection"""
        self.running = True
        while self.running:
            try:
                if not self.paused:
                    self.progress.emit("Capture de l'écran...")

                    # Utiliser la fonction globale de capture d'écran
                    try:
                        frame = capture_screen()
                        if frame is None:
                            self.error.emit("Erreur: capture_screen() a retourné None")
                            time.sleep(1)
                            continue
                    except Exception as capture_error:
                        self.error.emit(f"Erreur lors de la capture: {str(capture_error)}")
                        time.sleep(1)
                        continue

                    # Émettre le signal avec l'image capturée
                    try:
                        self.image_captured.emit(frame)
                    except Exception as emit_error:
                        print(f"⚠️ Erreur lors de l'émission du signal image: {emit_error}")

                    # Traiter l'image directement sans fichier temporaire
                    self.progress.emit("Détection en cours...")
                    try:
                        # Utiliser le mode rapide mais DÉSACTIVER le parallélisme temporairement pour diagnostiquer
                        results = self.detector.process_image_direct(frame, fast_mode=self.fast_mode, parallel=False)

                        if results is None:
                            self.error.emit("Erreur: process_image_direct() a retourné None")
                            time.sleep(1)
                            continue

                    except Exception as detection_error:
                        import traceback
                        error_details = traceback.format_exc()
                        print(f"❌ Erreur détaillée lors de la détection: {error_details}")
                        self.error.emit(f"Erreur lors de la détection: {str(detection_error)}")

                        # Incrémenter le compteur d'erreurs consécutives
                        self.consecutive_errors += 1
                        print(f"⚠️ Erreur consécutive #{self.consecutive_errors}/{self.max_consecutive_errors}")

                        # Nettoyage en cas d'erreur de détection
                        try:
                            if hasattr(self.detector, '_cleanup_gpu_memory'):
                                self.detector._cleanup_gpu_memory()
                        except Exception as cleanup_error:
                            print(f"⚠️ Erreur lors du nettoyage après erreur de détection: {cleanup_error}")

                        # Si trop d'erreurs consécutives, arrêter le thread
                        if self.consecutive_errors >= self.max_consecutive_errors:
                            print(f"❌ Trop d'erreurs consécutives ({self.consecutive_errors}), arrêt du thread de capture")
                            self.error.emit(f"Arrêt automatique après {self.consecutive_errors} erreurs consécutives")
                            self.running = False
                            break

                        time.sleep(1)
                        continue

                    # Émettre le signal de fin avec les résultats
                    try:
                        self.detection_finished.emit(results)
                    except Exception as emit_error:
                        print(f"⚠️ Erreur lors de l'émission du signal résultats: {emit_error}")

                    # Réinitialiser le compteur d'erreurs en cas de succès
                    self.consecutive_errors = 0

                    # Incrémenter le compteur de détections
                    self.detection_count += 1

                    # Nettoyage périodique de la mémoire GPU
                    if self.detection_count % self.cleanup_interval == 0:
                        try:
                            if hasattr(self.detector, '_cleanup_gpu_memory'):
                                self.detector._cleanup_gpu_memory()
                                print(f"🧹 Nettoyage périodique de la mémoire GPU (détection #{self.detection_count})")
                        except Exception as cleanup_error:
                            print(f"⚠️ Erreur lors du nettoyage périodique: {cleanup_error}")

                # Attendre l'intervalle spécifié
                time.sleep(self.interval)

            except Exception as e:
                import traceback
                error_details = traceback.format_exc()
                print(f"❌ Erreur générale dans ScreenCaptureThread: {error_details}")
                self.error.emit(f"Erreur générale lors de la capture d'écran: {str(e)}")
                time.sleep(2)  # Attendre plus longtemps en cas d'erreur générale

    def stop(self):
        """Arrête la capture d'écran et nettoie les ressources"""
        self.running = False
        self.wait()

        # Nettoyage final de la mémoire GPU
        try:
            if hasattr(self.detector, '_cleanup_gpu_memory'):
                self.detector._cleanup_gpu_memory()
                print("🧹 Nettoyage final de la mémoire GPU lors de l'arrêt")
        except Exception as cleanup_error:
            print(f"⚠️ Erreur lors du nettoyage final: {cleanup_error}")

        # Supprimer le fichier temporaire s'il existe
        if os.path.exists(self.temp_path):
            try:
                os.remove(self.temp_path)
            except:
                pass

        # Réinitialiser les compteurs
        self.consecutive_errors = 0
        self.detection_count = 0

    def pause(self):
        """Met en pause la capture d'écran"""
        self.paused = True
        self.progress.emit("Capture en pause")

    def resume(self):
        """Reprend la capture d'écran"""
        self.paused = False
        self.progress.emit("Capture reprise")

    def set_interval(self, interval):
        """Définit l'intervalle entre les captures

        Args:
            interval (float): Nouvel intervalle en secondes (minimum 0.1s)
        """
        self.interval = max(0.1, float(interval))  # Minimum 0.1 seconde
        self.progress.emit(f"Intervalle de capture défini à {self.interval:.1f}s")

    def set_fast_mode(self, fast_mode):
        """Définit le mode rapide

        Args:
            fast_mode (bool): Activer/désactiver le mode rapide
        """
        self.fast_mode = fast_mode
        mode_text = "activé" if fast_mode else "désactivé"
        self.progress.emit(f"Mode rapide {mode_text}")

class DetectorGUI(QMainWindow):
    """Interface graphique pour le module de détection

    Cette classe implémente l'interface graphique principale de l'application.
    Elle permet de :
    - Charger des images ou capturer l'écran
    - Sélectionner les régions à analyser
    - Lancer la détection de texte et de couleurs
    - Afficher et sauvegarder les résultats
    - Charger différentes configurations de régions
    """

    def __init__(self):
        super().__init__()

        # Créer les dossiers nécessaires s'ils n'existent pas
        os.makedirs('screenshots', exist_ok=True)
        os.makedirs('export', exist_ok=True)  # Dossier pour l'exportation des résultats

        # Barre de statut (créée avant init_ui)
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.status_bar.showMessage("Prêt")

        # Variables d'état
        self.current_image_path = None
        self.current_image = None  # Stocker l'image actuelle pour l'apprentissage
        self.current_results = None
        self.detection_thread = None
        self.screen_capture_thread = None
        self.region_checkboxes = {}
        self.capture_interval = 0.5  # Intervalle par défaut en secondes (optimisé pour la vitesse)
        self.zoom_factor = 2.2  # Facteur de zoom par défaut (comme dans calibration_simple)

        # Initialiser le conseiller poker si disponible
        self.poker_advisor = None
        self.use_advisor = False
        if POKER_ADVISOR_AVAILABLE:
            # Vérifier si l'argument --use-advisor est présent
            if '--use-advisor' in sys.argv or os.environ.get('USE_POKER_ADVISOR') == '1':
                self.use_advisor = True
                self.poker_advisor = PokerAdvisorLight()
                print("✅ Conseiller poker activé")

        # Initialiser le système d'apprentissage si disponible
        self.learning_system = None
        if LEARNING_SYSTEM_AVAILABLE:
            try:
                self.learning_system = LearningSystem()
                print("✅ Système d'apprentissage activé")
            except Exception as e:
                print(f"⚠️ Erreur lors de l'initialisation du système d'apprentissage: {e}")
                self.learning_system = None

        # Initialiser le système de surveillance si disponible
        self.app_monitor = None
        self.monitoring_enabled = False
        if MONITORING_AVAILABLE:
            # Vérifier si la surveillance est activée via variable d'environnement ou argument
            if os.environ.get('ENABLE_MONITORING') == '1' or '--enable-monitoring' in sys.argv:
                try:
                    self.app_monitor = AppMonitor()
                    self.monitoring_enabled = True
                    print("✅ Système de surveillance activé")
                    # Démarrer automatiquement la surveillance
                    self.app_monitor.start_monitoring()
                    print("✅ Surveillance démarrée automatiquement")
                except Exception as e:
                    print(f"⚠️ Erreur lors de l'initialisation du système de surveillance: {e}")
                    self.app_monitor = None
                    self.monitoring_enabled = False

        # Utiliser exactement le même chemin que calibration_simple.py
        # Chemin absolu vers le fichier de configuration de calibration
        calibration_config_path = os.path.abspath(os.path.join('..', 'Calibration', 'config', 'poker_advisor_config.json'))
        print(f"🔍 Recherche du fichier de configuration à: {calibration_config_path}")

        # Vérifier si le fichier de configuration de calibration existe
        if not os.path.exists(calibration_config_path):
            print(f"❌ ERREUR: Fichier de configuration de calibration non trouvé: {calibration_config_path}")
            QMessageBox.critical(self, "Erreur critique",
                               f"Le fichier de configuration de calibration est introuvable:\n{calibration_config_path}\n\n"
                               "Veuillez d'abord exécuter l'application 'calibration_simple.py' pour créer ce fichier.")
            # Définir config_path quand même pour éviter les erreurs
            config_path = calibration_config_path
        else:
            print(f"✅ Fichier de configuration de calibration trouvé: {calibration_config_path}")
            # Afficher le contenu du fichier pour vérification
            try:
                with open(calibration_config_path, 'r') as f:
                    config_content = json.load(f)
                print(f"✅ Contenu du fichier de configuration chargé avec succès")
                print(f"✅ Nombre de régions dans 'roi': {len(config_content.get('roi', {}))}")
                print(f"✅ Nombre de régions dans 'all_regions': {len(config_content.get('all_regions', {}))}")

                # Afficher les coordonnées des cartes pour vérification
                for i in range(1, 6):
                    card_key = f"card_{i}"
                    if card_key in config_content.get('roi', {}):
                        card_coords = config_content['roi'][card_key]
                        print(f"✅ Coordonnées de {card_key}: {card_coords}")
            except Exception as e:
                print(f"❌ Erreur lors de la lecture du fichier de configuration: {e}")

            config_path = calibration_config_path

        # Initialiser le détecteur avec la configuration de calibration
        try:
            if os.path.exists(calibration_config_path):
                # Initialiser le détecteur avec le chemin absolu du fichier de configuration
                print(f"🔧 Initialisation du détecteur avec le fichier: {calibration_config_path}")
                self.detector = Detector(calibration_config_path)
                self.detector.config_path = calibration_config_path  # Stocker le chemin pour référence future
                self.status_bar.showMessage(f"Configuration chargée depuis: {calibration_config_path}")
                print(f"✅ Détecteur initialisé avec la configuration de calibration")

                # Vérifier que le détecteur a bien chargé les régions
                if hasattr(self.detector, 'config') and 'roi' in self.detector.config:
                    num_regions = len(self.detector.config['roi'])
                    print(f"✅ Le détecteur a chargé {num_regions} régions")
                else:
                    print("⚠️ Le détecteur n'a pas chargé de régions")
            else:
                # Si le fichier n'existe pas, initialiser sans configuration
                self.detector = None
                self.status_bar.showMessage("Aucune configuration chargée")
                print("⚠️ Aucune configuration disponible")
        except Exception as e:
            QMessageBox.warning(self, "Avertissement",
                               f"Erreur lors de l'initialisation du détecteur: {str(e)}\n"
                               "L'application fonctionnera en mode limité.")
            self.detector = None
            print(f"❌ Erreur détaillée: {e}")

        # Configurer l'interface
        self.init_ui()

        # Afficher un message d'information au démarrage
        QMessageBox.information(self, "Bienvenue dans Poker Advisor",
                              "Bienvenue dans le module de détection de Poker Advisor !\n\n"
                              "Pour commencer, veuillez :\n"
                              "1. Ouvrir une image à analyser\n"
                              "2. Sélectionner les régions à analyser dans le panneau de droite\n"
                              "3. Cliquer sur 'Détecter' pour lancer l'analyse\n\n"
                              "Par défaut, aucune région n'est sélectionnée. Utilisez les boutons 'Tout cocher' pour sélectionner rapidement les régions par catégorie.")

    def init_ui(self):
        """Initialise l'interface utilisateur"""
        # Configurer la fenêtre principale
        self.setWindowTitle("Poker Advisor - Détection de cartes")
        self.setMinimumSize(1000, 700)

        # Créer la barre de menu
        self.create_menu_bar()

        # Créer la barre d'outils
        self.create_toolbar()

        # Widget central
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # Layout principal
        main_layout = QVBoxLayout(central_widget)

        # Splitter pour diviser l'écran
        splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(splitter)

        # Panneau de gauche (image)
        left_panel = QWidget()
        left_layout = QVBoxLayout(left_panel)

        # Zone d'affichage de l'image - AGRANDIE POUR MIEUX VOIR LES CAPTURES
        self.image_scroll_area = QScrollArea()
        self.image_scroll_area.setWidgetResizable(True)
        self.image_label = QLabel("Glissez-déposez une image ou utilisez le bouton 'Ouvrir'")
        self.image_label.setAlignment(Qt.AlignCenter)
        self.image_label.setMinimumSize(800, 600)  # Taille plus grande pour mieux voir
        self.image_label.setStyleSheet("border: 1px solid gray; background-color: #f0f0f0;")
        self.image_scroll_area.setWidget(self.image_label)
        left_layout.addWidget(self.image_scroll_area)

        # Boutons pour l'image
        image_buttons_layout = QHBoxLayout()

        self.open_button = QPushButton("Ouvrir une image")
        self.open_button.clicked.connect(self.open_image)
        image_buttons_layout.addWidget(self.open_button)

        self.detect_button = QPushButton("Détecter")
        self.detect_button.clicked.connect(self.start_detection)
        # Activer le bouton de détection dès le démarrage
        self.detect_button.setEnabled(True)
        image_buttons_layout.addWidget(self.detect_button)

        left_layout.addLayout(image_buttons_layout)

        # Boutons pour la capture d'écran
        screen_buttons_layout = QHBoxLayout()

        self.screen_capture_button = QPushButton("Capturer l'écran")
        self.screen_capture_button.clicked.connect(self.capture_screen_once)
        screen_buttons_layout.addWidget(self.screen_capture_button)

        self.realtime_capture_button = QPushButton("Détection en temps réel")
        self.realtime_capture_button.setCheckable(True)
        self.realtime_capture_button.clicked.connect(self.toggle_realtime_capture)
        screen_buttons_layout.addWidget(self.realtime_capture_button)

        left_layout.addLayout(screen_buttons_layout)

        # Contrôles pour la capture en temps réel
        realtime_controls_layout = QHBoxLayout()

        self.interval_label = QLabel("Intervalle (s):")
        realtime_controls_layout.addWidget(self.interval_label)

        self.interval_spinbox = QDoubleSpinBox()
        self.interval_spinbox.setRange(0.2, 10.0)  # Minimum réduit à 0.2s pour plus de vitesse
        self.interval_spinbox.setSingleStep(0.1)
        self.interval_spinbox.setValue(0.5)  # Valeur par défaut réduite à 0.5s
        self.interval_spinbox.setDecimals(1)
        self.interval_spinbox.setSuffix(" s")
        self.interval_spinbox.setToolTip("Intervalle entre les détections (0.2s minimum pour de meilleures performances)")
        self.interval_spinbox.valueChanged.connect(self.update_capture_interval)
        realtime_controls_layout.addWidget(self.interval_spinbox)

        # Ajouter une case à cocher pour le mode rapide
        self.fast_mode_checkbox = QCheckBox("Mode rapide")
        self.fast_mode_checkbox.setChecked(True)  # Activé par défaut pour de meilleures performances
        self.fast_mode_checkbox.setToolTip("Active le mode rapide pour améliorer la vitesse de détection\n(réduit le nombre d'appels OCR tout en conservant la précision)")
        self.fast_mode_checkbox.stateChanged.connect(self.update_fast_mode)
        realtime_controls_layout.addWidget(self.fast_mode_checkbox)

        # Ajouter un séparateur
        separator = QFrame()
        separator.setFrameShape(QFrame.VLine)
        separator.setFrameShadow(QFrame.Sunken)
        realtime_controls_layout.addWidget(separator)

        # Contrôle du zoom (comme dans calibration_simple)
        self.zoom_label = QLabel(f"Zoom: {self.zoom_factor:.1f}x")
        realtime_controls_layout.addWidget(self.zoom_label)

        # Nous utilisons la valeur fixe de 2.2 comme demandé
        zoom_info = QLabel("(fixé à 2.2x)")
        zoom_info.setStyleSheet("color: gray; font-style: italic;")
        realtime_controls_layout.addWidget(zoom_info)

        left_layout.addLayout(realtime_controls_layout)

        # Forcer l'activation des boutons de capture d'écran
        # mss est installé (version 10.0.0), donc on active les boutons
        print(f"🔍 État de SCREEN_CAPTURE_AVAILABLE: {SCREEN_CAPTURE_AVAILABLE}")

        # Forcer l'activation des boutons
        self.screen_capture_button.setEnabled(True)
        self.realtime_capture_button.setEnabled(True)
        self.interval_spinbox.setEnabled(True)
        print("✅ Boutons de capture d'écran forcés à l'état activé")

        # Panneau de droite (résultats)
        right_panel = QWidget()
        right_layout = QVBoxLayout(right_panel)

        # Zone des résultats
        results_group = QGroupBox("Résultats de la détection")
        results_layout = QVBoxLayout(results_group)

        self.results_scroll_area = QScrollArea()
        self.results_scroll_area.setWidgetResizable(True)
        self.results_widget = QWidget()
        self.results_layout = QVBoxLayout(self.results_widget)
        self.results_scroll_area.setWidget(self.results_widget)
        results_layout.addWidget(self.results_scroll_area)

        right_layout.addWidget(results_group)

        # Zone du conseiller poker (si disponible)
        if POKER_ADVISOR_AVAILABLE:
            advisor_group = QGroupBox("Conseiller Poker")
            advisor_layout = QVBoxLayout(advisor_group)

            # Zone de texte pour afficher les recommandations
            self.advisor_text = QTextEdit()
            self.advisor_text.setReadOnly(True)
            self.advisor_text.setMinimumHeight(200)
            self.advisor_text.setStyleSheet("background-color: #2D2D30; color: white;")
            advisor_layout.addWidget(self.advisor_text)

            # Boutons pour le conseiller
            advisor_buttons_layout = QHBoxLayout()

            self.refresh_advisor_button = QPushButton("Rafraîchir l'analyse")
            self.refresh_advisor_button.clicked.connect(self.refresh_advisor_analysis)
            self.refresh_advisor_button.setEnabled(False)
            advisor_buttons_layout.addWidget(self.refresh_advisor_button)

            # Bouton pour corriger manuellement les cartes
            self.correct_cards_button = QPushButton("Corriger les cartes")
            self.correct_cards_button.clicked.connect(self.show_card_correction_dialog)
            self.correct_cards_button.setEnabled(False)
            advisor_buttons_layout.addWidget(self.correct_cards_button)

            # Bouton pour réinitialiser le conseiller
            self.reset_advisor_button = QPushButton("Réinitialiser le conseiller")
            self.reset_advisor_button.clicked.connect(self.reset_advisor)
            self.reset_advisor_button.setEnabled(False)
            advisor_buttons_layout.addWidget(self.reset_advisor_button)

            # Bouton pour afficher le tableau de bord d'apprentissage
            self.learning_dashboard_button = QPushButton("📊 Tableau de bord")
            self.learning_dashboard_button.clicked.connect(self.show_learning_dashboard)
            self.learning_dashboard_button.setEnabled(self.learning_system is not None)
            self.learning_dashboard_button.setToolTip("Afficher les statistiques d'apprentissage et la précision")
            advisor_buttons_layout.addWidget(self.learning_dashboard_button)

            advisor_layout.addLayout(advisor_buttons_layout)

            # Ajouter le groupe au panneau de droite
            right_layout.addWidget(advisor_group)

            # Masquer le groupe si le conseiller n'est pas activé
            advisor_group.setVisible(self.use_advisor)

        # Options
        options_group = QGroupBox("Options")
        options_layout = QGridLayout(options_group)

        self.debug_checkbox = QCheckBox("Générer une image de débogage")
        self.debug_checkbox.setChecked(True)
        options_layout.addWidget(self.debug_checkbox, 0, 0)

        self.save_results_checkbox = QCheckBox("Sauvegarder les résultats (JSON)")
        self.save_results_checkbox.setChecked(True)
        options_layout.addWidget(self.save_results_checkbox, 1, 0)

        self.cuda_checkbox = QCheckBox("Utiliser CUDA (GPU) si disponible")
        self.cuda_checkbox.setChecked(True)
        options_layout.addWidget(self.cuda_checkbox, 2, 0)

        self.export_results_checkbox = QCheckBox("Exporter les résultats en temps réel (JSON et texte)")
        self.export_results_checkbox.setChecked(True)
        options_layout.addWidget(self.export_results_checkbox, 3, 0)

        # Option pour activer le conseiller poker (si disponible)
        if POKER_ADVISOR_AVAILABLE:
            self.advisor_checkbox = QCheckBox("Utiliser le conseiller poker intégré")
            self.advisor_checkbox.setChecked(self.use_advisor)
            self.advisor_checkbox.stateChanged.connect(self.toggle_advisor)
            options_layout.addWidget(self.advisor_checkbox, 4, 0)

        right_layout.addWidget(options_group)

        # Widget de surveillance (si disponible)
        if MONITORING_AVAILABLE and self.monitoring_enabled:
            monitoring_group = QGroupBox("Surveillance du système")
            monitoring_layout = QVBoxLayout(monitoring_group)

            # Zone d'affichage des statistiques
            self.monitoring_text = QTextEdit()
            self.monitoring_text.setReadOnly(True)
            self.monitoring_text.setMaximumHeight(120)
            self.monitoring_text.setStyleSheet("background-color: #1E1E1E; color: #00FF00; font-family: 'Courier New', monospace; font-size: 10px;")
            self.monitoring_text.setPlainText("Surveillance en cours...\nEn attente des premières données...")
            monitoring_layout.addWidget(self.monitoring_text)

            # Boutons de contrôle de la surveillance
            monitoring_buttons_layout = QHBoxLayout()

            self.monitoring_toggle_button = QPushButton("Arrêter surveillance")
            self.monitoring_toggle_button.clicked.connect(self.toggle_monitoring)
            self.monitoring_toggle_button.setStyleSheet("background-color: #FF4444; color: white;")
            monitoring_buttons_layout.addWidget(self.monitoring_toggle_button)

            self.monitoring_summary_button = QPushButton("Résumé")
            self.monitoring_summary_button.clicked.connect(self.show_monitoring_summary)
            monitoring_buttons_layout.addWidget(self.monitoring_summary_button)

            monitoring_layout.addLayout(monitoring_buttons_layout)

            right_layout.addWidget(monitoring_group)

            # Timer pour mettre à jour les statistiques de surveillance
            self.monitoring_timer = QTimer()
            self.monitoring_timer.timeout.connect(self.update_monitoring_display)
            self.monitoring_timer.start(2000)  # Mise à jour toutes les 2 secondes

        # Sélection des régions
        regions_group = QGroupBox("Régions à analyser")
        regions_layout = QVBoxLayout(regions_group)

        # Charger les régions disponibles
        self.load_available_regions(regions_layout)

        # Ajouter un bouton pour sélectionner/désélectionner toutes les régions
        select_buttons_layout = QHBoxLayout()

        select_all_button = QPushButton("Tout cocher")
        select_all_button.setFixedWidth(100)
        select_all_button.clicked.connect(self.select_all_regions)
        select_buttons_layout.addWidget(select_all_button)

        deselect_all_button = QPushButton("Tout décocher")
        deselect_all_button.setFixedWidth(100)
        deselect_all_button.clicked.connect(self.deselect_all_regions)
        select_buttons_layout.addWidget(deselect_all_button)

        regions_layout.addLayout(select_buttons_layout)

        # Ajouter un bouton pour recharger les régions
        reload_button = QPushButton("Recharger les régions")
        reload_button.clicked.connect(self.reload_regions)
        regions_layout.addWidget(reload_button)

        right_layout.addWidget(regions_group)

        # Boutons d'action
        action_buttons_layout = QHBoxLayout()

        self.save_button = QPushButton("Sauvegarder les résultats")
        self.save_button.clicked.connect(self.save_results)
        self.save_button.setEnabled(False)
        action_buttons_layout.addWidget(self.save_button)

        self.debug_button = QPushButton("Voir l'image de débogage")
        self.debug_button.clicked.connect(self.show_debug_image)
        self.debug_button.setEnabled(False)
        action_buttons_layout.addWidget(self.debug_button)

        right_layout.addLayout(action_buttons_layout)

        # Ajouter les panneaux au splitter
        splitter.addWidget(left_panel)
        splitter.addWidget(right_panel)
        splitter.setSizes([500, 500])  # Tailles initiales

        # Activer le glisser-déposer
        self.setAcceptDrops(True)

    def create_menu_bar(self):
        """Crée la barre de menu"""
        menu_bar = self.menuBar()

        # Menu Fichier
        file_menu = menu_bar.addMenu("Fichier")

        open_action = QAction("Ouvrir une image", self)
        open_action.triggered.connect(self.open_image)
        file_menu.addAction(open_action)

        load_config_action = QAction("Charger une configuration", self)
        load_config_action.triggered.connect(self.load_custom_config)
        file_menu.addAction(load_config_action)

        save_results_action = QAction("Sauvegarder les résultats", self)
        save_results_action.triggered.connect(self.save_results)
        save_results_action.setEnabled(False)
        self.save_results_action = save_results_action
        file_menu.addAction(save_results_action)

        file_menu.addSeparator()

        exit_action = QAction("Quitter", self)
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)

        # Menu Détection
        detection_menu = menu_bar.addMenu("Détection")

        detect_action = QAction("Détecter", self)
        detect_action.triggered.connect(self.start_detection)
        # Activer le bouton de détection dès le démarrage
        detect_action.setEnabled(True)
        self.detect_action = detect_action
        detection_menu.addAction(detect_action)

        debug_action = QAction("Voir l'image de débogage", self)
        debug_action.triggered.connect(self.show_debug_image)
        debug_action.setEnabled(False)
        self.debug_action = debug_action
        detection_menu.addAction(debug_action)

        # Menu Aide
        help_menu = menu_bar.addMenu("Aide")

        about_action = QAction("À propos", self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)

    def create_toolbar(self):
        """Crée la barre d'outils"""
        toolbar = QToolBar("Barre d'outils principale")
        toolbar.setIconSize(QSize(24, 24))
        self.addToolBar(toolbar)

        # Bouton Ouvrir
        open_action = QAction("Ouvrir", self)
        open_action.triggered.connect(self.open_image)
        toolbar.addAction(open_action)

        # Bouton Charger une configuration
        load_config_action = QAction("Charger config", self)
        load_config_action.triggered.connect(self.load_custom_config)
        toolbar.addAction(load_config_action)

        # Bouton Détecter
        detect_action = QAction("Détecter", self)
        detect_action.triggered.connect(self.start_detection)
        # Activer le bouton de détection dès le démarrage
        detect_action.setEnabled(True)
        self.toolbar_detect_action = detect_action
        toolbar.addAction(detect_action)

        toolbar.addSeparator()

        # Bouton Sauvegarder
        save_action = QAction("Sauvegarder", self)
        save_action.triggered.connect(self.save_results)
        save_action.setEnabled(False)
        self.toolbar_save_action = save_action
        toolbar.addAction(save_action)

        # Bouton Débogage
        debug_action = QAction("Débogage", self)
        debug_action.triggered.connect(self.show_debug_image)
        debug_action.setEnabled(False)
        self.toolbar_debug_action = debug_action
        toolbar.addAction(debug_action)



    def load_available_regions(self, layout):
        """Charge les régions disponibles dans la configuration et les organise par catégories

        Cette méthode charge les régions définies dans la configuration actuelle
        et crée des cases à cocher pour chaque région dans l'interface, organisées
        par catégories pour une meilleure lisibilité.

        Args:
            layout (QLayout): Layout dans lequel ajouter les cases à cocher
        """
        # Vider le layout existant
        while layout.count():
            item = layout.takeAt(0)
            widget = item.widget()
            if widget:
                widget.deleteLater()

        # Vider le dictionnaire des checkboxes
        self.region_checkboxes.clear()

        # Utiliser la configuration du détecteur actuel
        try:
            # Déterminer la source de configuration
            calibration_config_path = os.path.abspath(os.path.join('..', 'Calibration', 'config', 'poker_advisor_config.json'))

            if self.detector is None:
                # Si le détecteur n'est pas initialisé, essayer de charger directement la configuration de calibration
                if os.path.exists(calibration_config_path):
                    with open(calibration_config_path, 'r') as f:
                        config = json.load(f)
                    config_path = calibration_config_path
                    print(f"✅ Configuration chargée directement depuis: {config_path}")
                else:
                    # Si le fichier n'existe pas, afficher un message d'erreur
                    error_msg = f"Le fichier de configuration de calibration est introuvable: {calibration_config_path}"
                    print(f"❌ {error_msg}")
                    self.status_bar.showMessage(error_msg)
                    raise FileNotFoundError(error_msg)
            else:
                # Utiliser la configuration du détecteur
                config = self.detector.config
                config_path = getattr(self.detector, 'config_path', calibration_config_path)
                print(f"✅ Configuration chargée depuis le détecteur: {config_path}")

            # Créer un scroll area pour les régions
            scroll_area = QScrollArea()
            scroll_area.setWidgetResizable(True)
            scroll_widget = QWidget()
            scroll_layout = QVBoxLayout(scroll_widget)

            # Utiliser spécifiquement 'roi' pour les coordonnées
            region_config = config.get('roi', {})

            # Ajouter un label d'information
            info_label = QLabel(f"Configuration chargée depuis: {config_path}")
            info_label.setWordWrap(True)
            scroll_layout.addWidget(info_label)

            # Ajouter une ligne de séparation
            line = QFrame()
            line.setFrameShape(QFrame.HLine)
            line.setFrameShadow(QFrame.Sunken)
            scroll_layout.addWidget(line)

            # Définir les catégories et les préfixes correspondants
            categories = {
                "Cartes du board": ["card_"],
                "Cartes en main": ["hand_card_"],
                "Jetons et mises": ["chips_", "bet_amount"],
                "Joueurs adverses": ["player1_", "player2_", "player3_", "player4_", "player5_"],
                "Informations de jeu": ["game_info", "blinds_info", "timer", "dealer_button", "table_info"],
                "Interface utilisateur": ["action_buttons", "chat_box", "notes"],
                "Statistiques": ["stats_", "hand_strength", "odds_calculator"]
            }

            # Pour chaque catégorie, créer un groupe et ajouter les régions correspondantes
            for category_name, prefixes in categories.items():
                # Filtrer les régions qui correspondent à cette catégorie
                category_regions = []
                for name in sorted(region_config.keys()):
                    for prefix in prefixes:
                        if name.startswith(prefix) or name == prefix:
                            category_regions.append(name)
                            break

                # Si des régions correspondent à cette catégorie, créer un groupe
                if category_regions:
                    # Créer un groupe pour la catégorie
                    category_group = QGroupBox(category_name)
                    category_layout = QVBoxLayout(category_group)

                    # Ajouter des boutons pour sélectionner/désélectionner toutes les régions de cette catégorie
                    category_buttons_layout = QHBoxLayout()

                    select_all_category_button = QPushButton("Tout cocher")
                    select_all_category_button.setFixedWidth(100)
                    select_all_category_button.clicked.connect(lambda _, cat=category_regions: self.select_category_regions(cat, True))
                    category_buttons_layout.addWidget(select_all_category_button)

                    deselect_all_category_button = QPushButton("Tout décocher")
                    deselect_all_category_button.setFixedWidth(100)
                    deselect_all_category_button.clicked.connect(lambda _, cat=category_regions: self.select_category_regions(cat, False))
                    category_buttons_layout.addWidget(deselect_all_category_button)

                    category_layout.addLayout(category_buttons_layout)

                    # Ajouter une ligne de séparation
                    category_line = QFrame()
                    category_line.setFrameShape(QFrame.HLine)
                    category_line.setFrameShadow(QFrame.Sunken)
                    category_layout.addWidget(category_line)

                    # Ajouter les checkboxes pour chaque région de cette catégorie
                    for name in category_regions:
                        checkbox = QCheckBox(name)
                        checkbox.setChecked(False)  # Par défaut, toutes les régions sont décochées
                        checkbox.region_name = name  # Ajouter l'attribut region_name pour le conseiller
                        category_layout.addWidget(checkbox)
                        self.region_checkboxes[name] = checkbox

                    # Ajouter le groupe à la zone de défilement
                    scroll_layout.addWidget(category_group)

            # Vérifier s'il reste des régions non catégorisées
            uncategorized_regions = []
            for name in sorted(region_config.keys()):
                if name not in self.region_checkboxes:
                    uncategorized_regions.append(name)

            # Si des régions non catégorisées existent, les ajouter dans un groupe "Autres"
            if uncategorized_regions:
                other_group = QGroupBox("Autres régions")
                other_layout = QVBoxLayout(other_group)

                # Ajouter des boutons pour sélectionner/désélectionner toutes les régions de cette catégorie
                other_buttons_layout = QHBoxLayout()

                select_all_other_button = QPushButton("Tout cocher")
                select_all_other_button.setFixedWidth(100)
                select_all_other_button.clicked.connect(lambda _, cat=uncategorized_regions: self.select_category_regions(cat, True))
                other_buttons_layout.addWidget(select_all_other_button)

                deselect_all_other_button = QPushButton("Tout décocher")
                deselect_all_other_button.setFixedWidth(100)
                deselect_all_other_button.clicked.connect(lambda _, cat=uncategorized_regions: self.select_category_regions(cat, False))
                other_buttons_layout.addWidget(deselect_all_other_button)

                other_layout.addLayout(other_buttons_layout)

                # Ajouter une ligne de séparation
                other_line = QFrame()
                other_line.setFrameShape(QFrame.HLine)
                other_line.setFrameShadow(QFrame.Sunken)
                other_layout.addWidget(other_line)

                for name in uncategorized_regions:
                    checkbox = QCheckBox(name)
                    checkbox.setChecked(False)  # Par défaut, toutes les régions sont décochées
                    checkbox.region_name = name  # Ajouter l'attribut region_name pour le conseiller
                    other_layout.addWidget(checkbox)
                    self.region_checkboxes[name] = checkbox

                scroll_layout.addWidget(other_group)

            scroll_area.setWidget(scroll_widget)
            layout.addWidget(scroll_area)

            self.status_bar.showMessage(f"Régions chargées depuis {config_path}")
        except Exception as e:
            error_label = QLabel(f"Erreur lors du chargement des régions: {str(e)}")
            error_label.setWordWrap(True)
            layout.addWidget(error_label)
            self.status_bar.showMessage("Erreur lors du chargement des régions")

    def select_all_regions(self):
        """Sélectionne toutes les régions"""
        for checkbox in self.region_checkboxes.values():
            checkbox.setChecked(True)

    def deselect_all_regions(self):
        """Désélectionne toutes les régions"""
        for checkbox in self.region_checkboxes.values():
            checkbox.setChecked(False)

    def select_category_regions(self, region_names, checked):
        """Sélectionne ou désélectionne toutes les régions d'une catégorie

        Cette méthode permet de cocher ou décocher rapidement toutes les régions
        d'une catégorie spécifique, ce qui facilite la sélection des régions à analyser.

        Args:
            region_names (list): Liste des noms de régions à sélectionner/désélectionner
            checked (bool): True pour sélectionner, False pour désélectionner
        """
        if not region_names:
            return

        # Mettre à jour l'état de chaque case à cocher
        for name in region_names:
            if name in self.region_checkboxes:
                self.region_checkboxes[name].setChecked(checked)

        # Mettre à jour la barre de statut
        action = "cochées" if checked else "décochées"
        self.status_bar.showMessage(f"{len(region_names)} régions {action}")

    def load_custom_config(self):
        """Charge une configuration personnalisée à partir d'un fichier sélectionné par l'utilisateur

        Cette méthode ouvre une boîte de dialogue permettant à l'utilisateur de sélectionner
        un fichier de configuration JSON. Une fois le fichier sélectionné, elle initialise
        le détecteur avec cette configuration et met à jour l'interface.
        """
        # Déterminer le répertoire initial et le fichier suggéré
        calibration_config_path = os.path.abspath(os.path.join('..', 'Calibration', 'config', 'poker_advisor_config.json'))

        # Vérifier si le fichier de configuration de calibration existe
        initial_dir = ""
        if os.path.exists(calibration_config_path):
            initial_dir = os.path.dirname(calibration_config_path)
            print(f"✅ Suggestion du fichier de configuration de calibration: {calibration_config_path}")

        # Ouvrir une boîte de dialogue pour sélectionner un fichier
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "Charger une configuration",
            initial_dir,
            "Fichiers JSON (*.json);;Tous les fichiers (*)"
        )

        # Si l'utilisateur a annulé, ne rien faire
        if not file_path:
            return

        try:
            # Vérifier si le fichier existe
            if not os.path.exists(file_path):
                QMessageBox.critical(self, "Erreur", f"Le fichier n'existe pas: {file_path}")
                return

            print(f"🔍 Chargement de la configuration: {file_path}")

            # Initialiser le détecteur avec la nouvelle configuration
            use_cuda = self.cuda_checkbox.isChecked() if hasattr(self, 'cuda_checkbox') else None
            self.detector = Detector(file_path, use_cuda=use_cuda)

            # Mettre à jour la barre de statut
            self.status_bar.showMessage(f"Configuration chargée depuis: {file_path}")
            print(f"✅ Configuration personnalisée chargée: {file_path}")

            # Recharger les régions dans l'interface
            self.reload_regions()

            # Activer les boutons de détection si une image est chargée
            if hasattr(self, 'current_image_path') and self.current_image_path:
                self.detect_action.setEnabled(True)
                self.toolbar_detect_action.setEnabled(True)

            # Afficher un message de confirmation
            QMessageBox.information(
                self,
                "Configuration chargée",
                f"La configuration a été chargée avec succès depuis:\n{file_path}\n\n"
                f"Nombre de régions disponibles: {len(self.detector.config.get('roi', {}))}"
            )

        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors du chargement de la configuration: {str(e)}")
            print(f"❌ Erreur lors du chargement de la configuration: {e}")

    def reload_regions(self):
        """Recharge les régions depuis le fichier de configuration"""
        # Récupérer le layout du groupe de régions
        regions_group = self.findChild(QGroupBox, "Régions à analyser")
        if regions_group:
            layout = regions_group.layout()
            self.load_available_regions(layout)
        else:
            QMessageBox.warning(self, "Avertissement", "Impossible de trouver le groupe de régions.")

    def get_selected_regions(self):
        """Retourne la liste des régions sélectionnées"""
        selected_regions = []
        for name, checkbox in self.region_checkboxes.items():
            if checkbox.isChecked():
                selected_regions.append(name)
        return selected_regions

    def open_image(self):
        """Ouvre une boîte de dialogue pour sélectionner une image"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "Ouvrir une image", "", "Images (*.png *.jpg *.jpeg *.bmp);;Tous les fichiers (*)"
        )

        if file_path:
            self.load_image(file_path)

    def load_image(self, file_path):
        """Charge une image à partir d'un chemin de fichier"""
        try:
            # Charger l'image avec OpenCV
            image = cv2.imread(file_path)
            if image is None:
                QMessageBox.critical(self, "Erreur", f"Impossible de charger l'image: {file_path}")
                return

            # Convertir l'image pour l'affichage
            image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
            h, w, _ = image_rgb.shape  # _ pour ignorer le nombre de canaux

            # Appliquer le zoom (exactement comme dans calibration_simple)
            if self.zoom_factor != 1.0:
                # Appliquer le zoom à l'image (en conservant l'image originale)
                display_image = cv2.resize(image_rgb.copy(),
                                          (int(w * self.zoom_factor), int(h * self.zoom_factor)),
                                          interpolation=cv2.INTER_CUBIC)
                print(f"✅ Image zoomée avec un facteur de {self.zoom_factor:.1f}x")
            else:
                display_image = image_rgb.copy()

            # Redimensionner si nécessaire pour l'affichage
            max_display_width = 800
            max_display_height = 600

            h_display, w_display = display_image.shape[:2]
            if w_display > max_display_width or h_display > max_display_height:
                # Calculer le ratio pour conserver les proportions
                ratio = min(max_display_width / w_display, max_display_height / h_display)
                new_width = int(w_display * ratio)
                new_height = int(h_display * ratio)

                # Redimensionner l'image pour l'affichage
                display_image = cv2.resize(display_image, (new_width, new_height), interpolation=cv2.INTER_AREA)

            # Créer un QImage à partir des données de l'image
            qimg = QImage(display_image.data, display_image.shape[1], display_image.shape[0],
                         display_image.strides[0], QImage.Format_RGB888)

            # Créer un QPixmap à partir du QImage
            pixmap = QPixmap.fromImage(qimg)

            # Mettre à jour le label
            self.image_label.setPixmap(pixmap)
            self.image_label.setAlignment(Qt.AlignCenter)

            # Mettre à jour les variables d'état
            self.current_image_path = file_path
            self.current_image = image  # Stocker l'image originale pour l'apprentissage
            self.current_results = None

            # Activer le bouton de détection
            self.detect_button.setEnabled(True)
            self.detect_action.setEnabled(True)
            self.toolbar_detect_action.setEnabled(True)

            # Désactiver les boutons de résultats
            self.save_button.setEnabled(False)
            self.debug_button.setEnabled(False)
            self.save_results_action.setEnabled(False)
            self.debug_action.setEnabled(False)
            self.toolbar_save_action.setEnabled(False)
            self.toolbar_debug_action.setEnabled(False)

            # Effacer les résultats précédents
            self.clear_results()

            # Mettre à jour la barre de statut
            self.status_bar.showMessage(f"Image chargée: {os.path.basename(file_path)}")

        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors du chargement de l'image: {str(e)}")

    def start_detection(self):
        """Démarre la détection sur l'image actuelle - Version ultra-sécurisée"""
        print("🔍 DÉBUT DÉTECTION MANUELLE")

        try:
            if DEBUG_CRASH_AVAILABLE:
                log_debug("🎯 Début de la détection des cartes")

            print("✅ Debug log OK")

            if not self.current_image_path:
                if DEBUG_CRASH_AVAILABLE:
                    log_debug("❌ Aucune image chargée pour la détection")
                print("❌ Aucune image chargée")
                QMessageBox.warning(self, "Avertissement", "Veuillez d'abord charger une image.")
                return

            print(f"✅ Image chargée: {self.current_image_path}")

            if self.detector is None:
                if DEBUG_CRASH_AVAILABLE:
                    log_debug("❌ Détecteur non initialisé")
                print("❌ Détecteur non initialisé")
                QMessageBox.warning(self, "Avertissement",
                                  "Le détecteur n'a pas pu être initialisé. Impossible de détecter les cartes.")
                return

            print("✅ Détecteur disponible")

        except Exception as e:
            print(f"❌ ERREUR dans les vérifications initiales: {e}")
            import traceback
            traceback.print_exc()
            QMessageBox.critical(self, "Erreur", f"Erreur lors des vérifications: {e}")
            return

        # Récupérer les régions sélectionnées
        selected_regions = self.get_selected_regions()
        if not selected_regions:
            QMessageBox.warning(self, "Aucune région sélectionnée",
                               "Veuillez cocher au moins une région à analyser.\n\n"
                               "Utilisez les cases à cocher dans la section 'Régions à analyser' pour sélectionner les zones d'intérêt.")
            return

        # Réinitialiser le détecteur avec les régions sélectionnées
        try:
            # Récupérer l'état de la case à cocher CUDA
            use_cuda = self.cuda_checkbox.isChecked()

            # Utiliser uniquement la configuration de calibration
            calibration_config_path = os.path.abspath(os.path.join('..', 'Calibration', 'config', 'poker_advisor_config.json'))
            print(f"🔍 Recherche du fichier de configuration à: {calibration_config_path}")

            if not os.path.exists(calibration_config_path):
                error_msg = f"Le fichier de configuration de calibration est introuvable: {calibration_config_path}"
                self.status_bar.showMessage(error_msg)
                print(f"❌ {error_msg}")
                QMessageBox.critical(self, "Erreur critique",
                                   f"{error_msg}\n\nVeuillez d'abord exécuter l'application 'calibration_simple.py'.")
                return

            config_path = calibration_config_path
            self.status_bar.showMessage(f"Utilisation de la configuration de calibration: {config_path}")
            print(f"✅ Utilisation de la configuration de calibration: {config_path}")

            # Initialiser le détecteur avec les régions sélectionnées et l'option CUDA
            self.detector = Detector(config_path, selected_regions, use_cuda)
        except Exception as e:
            QMessageBox.critical(self, "Erreur",
                               f"Erreur lors de l'initialisation du détecteur: {str(e)}")
            return

        # Désactiver les boutons pendant la détection
        self.detect_button.setEnabled(False)
        self.detect_action.setEnabled(False)
        self.toolbar_detect_action.setEnabled(False)

        # Mettre à jour la barre de statut
        self.status_bar.showMessage("Détection en cours...")

        # MODE SYNCHRONE - Éviter complètement les threads pour éliminer les crashes
        try:
            print("🔄 DÉTECTION EN MODE SYNCHRONE (sans thread)")

            # Faire la détection directement dans le thread principal
            print("🔍 Début de la détection synchrone...")

            # Traiter les événements Qt pour maintenir l'interface responsive
            from PyQt5.QtWidgets import QApplication
            QApplication.processEvents()

            # Faire la détection directement avec la bonne méthode
            results = self.detector.process_image(self.current_image_path)
            print("✅ Détection synchrone terminée")

            # Traiter les événements Qt
            QApplication.processEvents()

            # Appeler directement detection_finished avec les résultats
            print("📊 Traitement des résultats...")
            self.detection_finished(results)
            print("✅ Détection synchrone complète")

        except Exception as e:
            print(f"❌ ERREUR lors de la détection synchrone: {e}")
            import traceback
            traceback.print_exc()

            # Réactiver les boutons en cas d'erreur
            try:
                self.detect_button.setEnabled(True)
                self.detect_action.setEnabled(True)
                self.toolbar_detect_action.setEnabled(True)
                self.status_bar.showMessage("Erreur lors de la détection")
            except:
                pass

            QMessageBox.critical(self, "Erreur", f"Erreur lors de la détection: {e}")

    def detection_finished(self, results):
        """Appelé lorsque la détection est terminée - Version ultra-robuste avec nettoyage CUDA"""
        try:
            print("🏁 DÉBUT detection_finished")

            # Protection contre les résultats invalides
            if not isinstance(results, dict):
                print(f"⚠️ Résultats invalides reçus: {type(results)}")
                results = {}

            print("✅ Résultats validés")

            # Mettre à jour les variables d'état
            self.current_results = results

            # Afficher les résultats avec protection
            try:
                self.display_results(results)
                print("✅ Résultats affichés")
            except Exception as e:
                print(f"⚠️ Erreur affichage résultats: {e}")

            # Mettre à jour l'analyse du conseiller poker si activé (avec protection ultra-renforcée)
            if self.use_advisor and self.poker_advisor:
                try:
                    self.update_advisor_analysis(results)
                    print("✅ Analyse conseiller terminée")
                except Exception as e:
                    print(f"⚠️ Erreur analyse conseiller: {e}")
                    # Ne pas faire crasher l'application
                    try:
                        if hasattr(self, 'advisor_text'):
                            self.advisor_text.setText(f"Erreur temporaire d'analyse: {e}")
                    except:
                        pass

            print("✅ Traitement principal terminé")

        except Exception as e:
            print(f"❌ Erreur critique dans detection_finished: {e}")
            import traceback
            traceback.print_exc()

        finally:
            print("🧹 NETTOYAGE FORCÉ APRÈS DÉTECTION")

            # NETTOYAGE CUDA FORCÉ
            try:
                import torch
                if torch.cuda.is_available():
                    print("🔥 Nettoyage CUDA...")
                    torch.cuda.empty_cache()
                    torch.cuda.synchronize()
                    print("✅ Cache CUDA vidé")
            except Exception as e:
                print(f"⚠️ Erreur nettoyage CUDA: {e}")

            # NETTOYAGE MÉMOIRE PYTHON FORCÉ
            try:
                import gc
                print("🗑️ Garbage collection forcé...")
                collected = gc.collect()
                print(f"✅ {collected} objets collectés")
            except Exception as e:
                print(f"⚠️ Erreur garbage collection: {e}")

            # NETTOYAGE DÉTECTEUR
            try:
                if hasattr(self, 'detector') and self.detector:
                    print("🔧 Nettoyage détecteur...")
                    # Forcer la libération des ressources du détecteur
                    if hasattr(self.detector, '_cleanup_gpu_memory'):
                        self.detector._cleanup_gpu_memory()
                    print("✅ Détecteur nettoyé")
            except Exception as e:
                print(f"⚠️ Erreur nettoyage détecteur: {e}")

            # RÉACTIVATION DES BOUTONS
            try:
                print("🔄 Réactivation des boutons...")
                # Activer les boutons de résultats
                self.save_button.setEnabled(True)
                self.save_results_action.setEnabled(True)
                self.toolbar_save_action.setEnabled(True)

                # Activer les boutons du conseiller si le conseiller est activé
                if self.use_advisor:
                    if hasattr(self, 'correct_cards_button'):
                        self.correct_cards_button.setEnabled(True)
                    if hasattr(self, 'reset_advisor_button'):
                        self.reset_advisor_button.setEnabled(True)

                # Réactiver le bouton de détection
                self.detect_button.setEnabled(True)
                self.detect_action.setEnabled(True)
                self.toolbar_detect_action.setEnabled(True)

                # Mettre à jour la barre de statut
                self.status_bar.showMessage("Détection terminée - Prêt pour nouvelle détection")
                print("✅ Boutons réactivés")

            except Exception as e:
                print(f"⚠️ Erreur lors de la réactivation des boutons: {e}")

            print("🏁 FIN detection_finished - Mémoire nettoyée")

        # Générer l'image de débogage si demandé (en dehors du try/finally)
        try:
            if hasattr(self, 'debug_checkbox') and self.debug_checkbox.isChecked():
                debug_path = "detection_debug.jpg"
                self.detector.generate_debug_image(self.current_image_path, results, debug_path)
                self.debug_button.setEnabled(True)
                self.debug_action.setEnabled(True)
        except Exception as e:
            print(f"⚠️ Erreur génération debug: {e}")
            self.toolbar_debug_action.setEnabled(True)

        # Sauvegarder les résultats si demandé
        if self.save_results_checkbox.isChecked():
            output_path = "detection_results.json"
            self.detector.save_results(results, output_path)

        # Exporter les résultats en temps réel si demandé
        if self.export_results_checkbox.isChecked():
            self.export_realtime_results(results)

        # Réactiver le bouton de détection
        self.detect_button.setEnabled(True)
        self.detect_action.setEnabled(True)
        self.toolbar_detect_action.setEnabled(True)

        # Mettre à jour la barre de statut
        self.status_bar.showMessage("Détection terminée")

    def detection_error(self, error_message):
        """Appelé en cas d'erreur lors de la détection"""
        QMessageBox.critical(self, "Erreur", error_message)

        # Réactiver le bouton de détection
        self.detect_button.setEnabled(True)
        self.detect_action.setEnabled(True)
        self.toolbar_detect_action.setEnabled(True)

        # Mettre à jour la barre de statut
        self.status_bar.showMessage("Erreur lors de la détection")

    def update_status(self, message):
        """Met à jour la barre de statut"""
        self.status_bar.showMessage(message)

    def clear_results(self):
        """Efface les résultats précédents"""
        # Supprimer tous les widgets de la zone de résultats
        while self.results_layout.count():
            item = self.results_layout.takeAt(0)
            widget = item.widget()
            if widget:
                widget.deleteLater()

    def display_results(self, results):
        """Affiche les résultats de la détection"""
        # Effacer les résultats précédents
        self.clear_results()

        if not results:
            # Aucun résultat
            no_results_label = QLabel("Aucun résultat trouvé.")
            no_results_label.setAlignment(Qt.AlignCenter)
            self.results_layout.addWidget(no_results_label)
            return

        # Ajouter un titre
        title_label = QLabel("Résultats de la détection")
        title_label.setStyleSheet("font-weight: bold; font-size: 14px;")
        title_label.setAlignment(Qt.AlignCenter)
        self.results_layout.addWidget(title_label)

        # Ajouter une ligne de séparation
        line = QFrame()
        line.setFrameShape(QFrame.HLine)
        line.setFrameShadow(QFrame.Sunken)
        self.results_layout.addWidget(line)

        # Afficher les résultats pour chaque région
        for name, data in results.items():
            # Créer un groupe pour la région
            region_group = QGroupBox(name)
            region_layout = QVBoxLayout(region_group)

            # Texte détecté
            text_label = QLabel(f"Texte: {data['text'] if data['text'] else 'Aucun'}")
            region_layout.addWidget(text_label)

            # Couleurs détectées
            colors_text = ", ".join(data['colors']) if data['colors'] else "Aucune"
            colors_label = QLabel(f"Couleurs: {colors_text}")
            region_layout.addWidget(colors_label)

            # Ajouter le groupe à la zone de résultats
            self.results_layout.addWidget(region_group)

        # Ajouter un espace en bas
        self.results_layout.addStretch()

    def save_results(self):
        """Sauvegarde les résultats dans un fichier JSON"""
        if not self.current_results:
            QMessageBox.warning(self, "Avertissement", "Aucun résultat à sauvegarder.")
            return

        # Ouvrir une boîte de dialogue pour sélectionner le fichier de sortie
        file_path, _ = QFileDialog.getSaveFileName(
            self, "Sauvegarder les résultats", "detection_results.json", "Fichiers JSON (*.json);;Tous les fichiers (*)"
        )

        if file_path:
            # Sauvegarder les résultats
            success = self.detector.save_results(self.current_results, file_path)

            if success:
                self.status_bar.showMessage(f"Résultats sauvegardés dans {file_path}")
            else:
                QMessageBox.critical(self, "Erreur", "Erreur lors de la sauvegarde des résultats.")

    def show_debug_image(self):
        """Affiche l'image de débogage"""
        debug_path = "detection_debug.jpg"

        if not os.path.exists(debug_path):
            QMessageBox.warning(self, "Avertissement", "L'image de débogage n'existe pas.")
            return

        # Créer une nouvelle fenêtre pour afficher l'image de débogage
        from PyQt5.QtWidgets import QDialog

        debug_dialog = QDialog(self)
        debug_dialog.setWindowTitle("Image de débogage")
        debug_dialog.setMinimumSize(800, 600)

        dialog_layout = QVBoxLayout(debug_dialog)

        # Zone de défilement pour l'image
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)

        # Label pour l'image
        image_label = QLabel()

        # Charger l'image
        pixmap = QPixmap(debug_path)
        image_label.setPixmap(pixmap)

        scroll_area.setWidget(image_label)
        dialog_layout.addWidget(scroll_area)

        # Bouton pour fermer
        close_button = QPushButton("Fermer")
        close_button.clicked.connect(debug_dialog.close)
        dialog_layout.addWidget(close_button)

        # Afficher la fenêtre
        debug_dialog.exec_()

    def show_about(self):
        """Affiche la boîte de dialogue À propos"""
        QMessageBox.about(
            self,
            "À propos",
            "Poker Advisor - Module de détection\n\n"
            "Version 1.0\n\n"
            "Ce module permet de détecter le texte et les couleurs dans des régions spécifiques d'une image."
        )

    def dragEnterEvent(self, event: QDragEnterEvent):
        """Gère l'événement d'entrée de glisser-déposer"""
        if event.mimeData().hasUrls():
            event.acceptProposedAction()

    def dropEvent(self, event: QDropEvent):
        """Gère l'événement de dépôt de glisser-déposer"""
        urls = event.mimeData().urls()
        if urls and urls[0].isLocalFile():
            file_path = urls[0].toLocalFile()
            # Vérifier si c'est une image
            if file_path.lower().endswith(('.png', '.jpg', '.jpeg', '.bmp')):
                self.load_image(file_path)
            else:
                QMessageBox.warning(self, "Avertissement", "Veuillez déposer une image valide.")

    def update_capture_interval(self, value):
        """Met à jour l'intervalle de capture d'écran"""
        self.capture_interval = float(value)
        if self.screen_capture_thread and self.screen_capture_thread.isRunning():
            self.screen_capture_thread.set_interval(self.capture_interval)
            self.status_bar.showMessage(f"Intervalle de capture mis à jour: {self.capture_interval} secondes")

    def capture_screen_once(self):
        """Capture l'écran une seule fois et lance la détection"""
        # mss est installé (version 10.0.0), donc on ignore cette vérification
        print("🔍 Capture d'écran unique démarrée")

        try:
            # Vérifier si des régions sont sélectionnées
            selected_regions = self.get_selected_regions()
            if not selected_regions:
                QMessageBox.warning(self, "Aucune région sélectionnée",
                                   "Veuillez sélectionner au moins une région à analyser avant de lancer la détection.")
                return

            # Capturer l'écran
            self.status_bar.showMessage("Capture de l'écran en cours...")

            # Utiliser la fonction globale de capture d'écran
            print("🔍 Appel de la fonction capture_screen() depuis capture_screen_once()...")
            frame = capture_screen()
            # Pas besoin de vérifier si frame est None car capture_screen retourne toujours une image
            # (soit l'écran capturé, soit une image noire de secours)

            print("✅ Capture d'écran unique réussie dans capture_screen_once()")

            # Extraire les régions sélectionnées
            regions_dict = self.extract_selected_regions(frame, selected_regions)

            # Créer une prévisualisation des régions
            preview = self.create_regions_preview(regions_dict)

            if preview is not None:
                # Afficher la prévisualisation
                preview_rgb = cv2.cvtColor(preview, cv2.COLOR_BGR2RGB)
                h, w = preview_rgb.shape[:2]

                # Nous n'appliquons pas de zoom à l'image de prévisualisation
                # car les coordonnées des régions sont déjà correctes
                print(f"✅ Utilisation du facteur de zoom {self.zoom_factor:.1f}x pour les coordonnées")

                # Redimensionner si nécessaire pour l'affichage
                max_display_width = 800
                max_display_height = 600

                if w > max_display_width or h > max_display_height:
                    # Calculer le ratio pour conserver les proportions
                    ratio = min(max_display_width / w, max_display_height / h)
                    new_width = int(w * ratio)
                    new_height = int(h * ratio)

                    # Redimensionner l'image pour l'affichage
                    preview_rgb = cv2.resize(preview_rgb, (new_width, new_height), interpolation=cv2.INTER_AREA)
                    h, w = preview_rgb.shape[:2]

                # Créer un QImage à partir des données de l'image
                qimg = QImage(preview_rgb.data, w, h, preview_rgb.strides[0], QImage.Format_RGB888)

                # Créer un QPixmap à partir du QImage
                pixmap = QPixmap.fromImage(qimg)

                # Mettre à jour le label
                self.image_label.setPixmap(pixmap)
                self.image_label.setAlignment(Qt.AlignCenter)

            # Sauvegarder temporairement l'image complète pour la détection
            temp_path = os.path.join("screenshots", f"screenshot_{int(time.time())}.jpg")
            cv2.imwrite(temp_path, frame)

            # Mettre à jour le chemin de l'image actuelle
            self.current_image_path = temp_path
            self.current_image = frame  # Stocker l'image capturée pour l'apprentissage

            # Activer le bouton de détection
            self.detect_button.setEnabled(True)
            self.detect_action.setEnabled(True)
            self.toolbar_detect_action.setEnabled(True)

            # Lancer la détection
            self.start_detection()

        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors de la capture d'écran: {str(e)}")

    def toggle_realtime_capture(self, checked):
        """Active ou désactive la capture d'écran en temps réel"""
        # mss est installé (version 10.0.0), donc on ignore cette vérification
        print(f"🔍 Toggle realtime capture: {checked}")

        if checked:
            # Vérifier si des régions sont sélectionnées
            selected_regions = self.get_selected_regions()
            if not selected_regions:
                QMessageBox.warning(self, "Aucune région sélectionnée",
                                   "Veuillez sélectionner au moins une région à analyser avant de lancer la détection en temps réel.")
                self.realtime_capture_button.setChecked(False)
                return

            # Démarrer la capture en temps réel
            self.start_realtime_capture()
        else:
            # Arrêter la capture en temps réel
            self.stop_realtime_capture()

    def start_realtime_capture(self):
        """Démarre la capture d'écran en temps réel"""
        try:
            # Récupérer les régions sélectionnées
            selected_regions = self.get_selected_regions()

            # Récupérer l'état de la case à cocher CUDA
            use_cuda = self.cuda_checkbox.isChecked()

            # Utiliser uniquement la configuration de calibration
            calibration_config_path = os.path.abspath(os.path.join('..', 'Calibration', 'config', 'poker_advisor_config.json'))
            print(f"🔍 Recherche du fichier de configuration à: {calibration_config_path}")

            if not os.path.exists(calibration_config_path):
                error_msg = f"Le fichier de configuration de calibration est introuvable: {calibration_config_path}"
                self.status_bar.showMessage(error_msg)
                print(f"❌ {error_msg}")
                QMessageBox.critical(self, "Erreur critique",
                                   f"{error_msg}\n\nVeuillez d'abord exécuter l'application 'calibration_simple.py'.")
                self.realtime_capture_button.setChecked(False)
                return

            # Vérifier le contenu du fichier
            try:
                with open(calibration_config_path, 'r') as f:
                    config_content = json.load(f)
                print(f"✅ Fichier de configuration chargé avec succès")
                print(f"✅ Nombre de régions dans 'roi': {len(config_content.get('roi', {}))}")

                # Afficher les coordonnées des cartes pour vérification
                for i in range(1, 6):
                    card_key = f"card_{i}"
                    if card_key in config_content.get('roi', {}):
                        card_coords = config_content['roi'][card_key]
                        print(f"✅ Coordonnées de {card_key}: {card_coords}")
            except Exception as e:
                print(f"❌ Erreur lors de la lecture du fichier de configuration: {e}")

            config_path = calibration_config_path
            self.status_bar.showMessage(f"Utilisation de la configuration de calibration: {config_path}")
            print(f"✅ Utilisation de la configuration de calibration: {config_path}")

            # Initialiser le détecteur avec les régions sélectionnées et l'option CUDA
            self.detector = Detector(config_path, selected_regions, use_cuda)

            # Créer et démarrer le thread de capture d'écran avec le mode rapide
            fast_mode = self.fast_mode_checkbox.isChecked()
            self.screen_capture_thread = ScreenCaptureThread(self.detector, self.capture_interval, fast_mode)
            self.screen_capture_thread.image_captured.connect(self.update_captured_image)
            self.screen_capture_thread.detection_finished.connect(self.detection_finished)
            self.screen_capture_thread.progress.connect(self.update_status)
            self.screen_capture_thread.error.connect(self.detection_error)
            self.screen_capture_thread.start()

            # Mettre à jour l'interface
            self.status_bar.showMessage("Détection en temps réel démarrée")
            self.interval_spinbox.setEnabled(True)

            # Désactiver certains boutons pendant la capture en temps réel
            self.open_button.setEnabled(False)
            self.detect_button.setEnabled(False)
            self.screen_capture_button.setEnabled(False)

        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors du démarrage de la capture en temps réel: {str(e)}")
            self.realtime_capture_button.setChecked(False)

    def stop_realtime_capture(self):
        """Arrête la capture d'écran en temps réel"""
        if self.screen_capture_thread and self.screen_capture_thread.isRunning():
            self.screen_capture_thread.stop()
            self.screen_capture_thread = None

            # Mettre à jour l'interface
            self.status_bar.showMessage("Détection en temps réel arrêtée")

            # Réactiver les boutons
            self.open_button.setEnabled(True)
            self.detect_button.setEnabled(True)
            self.screen_capture_button.setEnabled(True)

    def extract_selected_regions(self, frame, selected_regions):
        """Extrait les régions sélectionnées de l'image capturée

        Args:
            frame: Image capturée (format OpenCV)
            selected_regions: Liste des noms des régions sélectionnées

        Returns:
            Un dictionnaire contenant les images des régions extraites
        """
        try:
            # Récupérer la configuration des régions depuis le détecteur ou la configuration de calibration
            if self.detector is None:
                # Si le détecteur n'est pas initialisé, utiliser la configuration de calibration
                calibration_config_path = os.path.abspath(os.path.join('..', 'Calibration', 'config', 'poker_advisor_config.json'))
                print(f"🔍 Recherche du fichier de configuration à: {calibration_config_path}")

                if not os.path.exists(calibration_config_path):
                    error_msg = f"Le fichier de configuration de calibration est introuvable: {calibration_config_path}"
                    print(f"❌ {error_msg}")
                    self.status_bar.showMessage(error_msg)
                    raise FileNotFoundError(error_msg)

                # Charger le fichier de configuration
                try:
                    with open(calibration_config_path, 'r') as f:
                        config = json.load(f)
                    print(f"✅ Configuration chargée depuis: {calibration_config_path}")

                    # Vérifier le contenu du fichier
                    if 'roi' in config:
                        print(f"✅ Nombre de régions dans 'roi': {len(config['roi'])}")
                        # Afficher les coordonnées des cartes pour vérification
                        for i in range(1, 6):
                            card_key = f"card_{i}"
                            if card_key in config['roi']:
                                card_coords = config['roi'][card_key]
                                print(f"✅ Coordonnées de {card_key}: {card_coords}")
                    else:
                        print("⚠️ Aucune région 'roi' trouvée dans la configuration")
                except Exception as e:
                    print(f"❌ Erreur lors de la lecture du fichier de configuration: {e}")
                    raise
            else:
                # Utiliser la configuration du détecteur
                config = self.detector.config
                print("✅ Configuration chargée depuis le détecteur actuel")

                # Vérifier le contenu de la configuration
                if 'roi' in config:
                    print(f"✅ Nombre de régions dans 'roi': {len(config['roi'])}")
                else:
                    print("⚠️ Aucune région 'roi' trouvée dans la configuration du détecteur")

            # Utiliser spécifiquement 'roi' pour les coordonnées
            region_config = config.get('roi', {})

            # Extraire les régions
            extracted_regions = {}
            for name in selected_regions:
                if name in region_config:
                    region = region_config[name]

                    # Extraire les coordonnées (gérer les deux formats possibles)
                    if 'x' in region and 'y' in region:
                        x, y = region['x'], region['y']
                        width, height = region['width'], region['height']
                    elif 'left' in region and 'top' in region:
                        x, y = region['left'], region['top']
                        width, height = region['width'], region['height']
                    else:
                        continue

                    # Utiliser les coordonnées telles quelles
                    print(f"Coordonnées pour {name}: x={x}, y={y}, w={width}, h={height}")

                    # Vérifier que les coordonnées sont valides
                    frame_height, frame_width = frame.shape[:2]
                    if x >= 0 and y >= 0 and x + width <= frame_width and y + height <= frame_height:
                        # Extraire la région
                        region_image = frame[y:y+height, x:x+width]
                        extracted_regions[name] = region_image
                    else:
                        print(f"⚠️ Coordonnées hors limites pour {name}: x={x}, y={y}, w={width}, h={height}, frame={frame_width}x{frame_height}")

            return extracted_regions

        except Exception as e:
            import traceback
            print(f"❌ Erreur lors de l'extraction des régions: {e}")
            print(f"❌ Détails de l'erreur: {traceback.format_exc()}")
            self.status_bar.showMessage(f"Erreur lors de l'extraction des régions: {str(e)}")
            return {}

    def create_regions_preview(self, regions_dict):
        """Crée une image de prévisualisation des régions sélectionnées

        Args:
            regions_dict: Dictionnaire contenant les images des régions extraites

        Returns:
            Une image combinée de toutes les régions (format OpenCV)
        """
        try:
            if not regions_dict:
                return None

            # Créer un fond blanc pour la prévisualisation - DIMENSIONS AGRANDIES
            preview_width = 1600  # Largeur encore plus grande pour éviter les coupures
            preview_height = 1200  # Hauteur encore plus grande pour plus d'espace
            preview = np.ones((preview_height, preview_width, 3), dtype=np.uint8) * 240  # Fond gris clair

            # Paramètres optimisés pour une meilleure visibilité
            margin = 50  # Marge plus grande pour éviter les coupures
            spacing_x = 80  # Espacement horizontal plus généreux
            spacing_y = 120  # Espacement vertical plus généreux
            cards_per_row = 4  # Plus de cartes par ligne pour utiliser l'espace

            # Calculer la taille maximale des régions pour un meilleur placement
            max_width = 0
            max_height = 0
            for region_image in regions_dict.values():
                h, w = region_image.shape[:2]
                max_width = max(max_width, w)
                max_height = max(max_height, h)

            # Ajouter chaque région à la prévisualisation
            i = 0
            for name, region_image in regions_dict.items():
                # Calculer la position dans la grille
                col = i % cards_per_row
                row = i // cards_per_row

                # Obtenir les dimensions de l'image
                h, w = region_image.shape[:2]

                # Calculer les coordonnées avec un espacement uniforme
                x = margin + col * (max_width + spacing_x)
                y = margin + row * (max_height + spacing_y)  # Espace supplémentaire pour le texte

                # Vérifier si la région dépasse les limites de la prévisualisation
                if y + h >= preview_height or x + w >= preview_width:
                    # Augmenter la taille de la prévisualisation si nécessaire
                    new_width = max(preview_width, x + w + margin)
                    new_height = max(preview_height, y + h + margin + 30)  # +30 pour le texte
                    new_preview = np.ones((new_height, new_width, 3), dtype=np.uint8) * 240
                    new_preview[:preview_height, :preview_width] = preview
                    preview = new_preview
                    preview_width, preview_height = new_width, new_height

                # Ajouter la région à la prévisualisation
                preview[y:y+h, x:x+w] = region_image

                # Déterminer la couleur du texte en fonction du nom de la région
                text_color = (0, 0, 255)  # Bleu par défaut

                # Utiliser des couleurs spécifiques pour les différentes catégories de régions
                if 'card_' in name:
                    text_color = (0, 0, 255)  # Rouge pour les cartes
                elif 'hand_' in name:
                    text_color = (255, 0, 0)  # Bleu pour les cartes en main
                elif 'mise_' in name:
                    text_color = (0, 255, 0)  # Vert pour les mises

                # Ajouter un rectangle autour de la région pour mieux la délimiter
                cv2.rectangle(preview, (x-2, y-2), (x+w+2, y+h+2), text_color, 1)

                # Ajouter le nom de la région avec un fond pour une meilleure lisibilité
                text_size = cv2.getTextSize(name, cv2.FONT_HERSHEY_SIMPLEX, 0.6, 1)[0]
                cv2.rectangle(preview, (x, y+h+5), (x+text_size[0], y+h+5+text_size[1]+10), (240, 240, 240), -1)
                cv2.putText(preview, name, (x, y+h+20),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.6, text_color, 1, cv2.LINE_AA)

                i += 1

            return preview

        except Exception as e:
            print(f"Erreur lors de la création de la prévisualisation: {e}")
            self.status_bar.showMessage(f"Erreur lors de la création de la prévisualisation: {str(e)}")
            return None

    def update_captured_image(self, frame):
        """Met à jour l'image affichée avec la capture d'écran"""
        try:
            # Extraire les régions sélectionnées
            selected_regions = self.get_selected_regions()
            regions_dict = self.extract_selected_regions(frame, selected_regions)

            # Créer une prévisualisation des régions
            preview = self.create_regions_preview(regions_dict)

            if preview is not None:
                # Convertir l'image pour l'affichage (BGR -> RGB)
                preview_rgb = cv2.cvtColor(preview, cv2.COLOR_BGR2RGB)

                # Redimensionner l'image si elle est trop grande pour l'interface
                max_display_width = 1200  # Largeur maximale pour l'affichage
                max_display_height = 800  # Hauteur maximale pour l'affichage

                h, w = preview_rgb.shape[:2]
                if w > max_display_width or h > max_display_height:
                    # Calculer le ratio pour conserver les proportions
                    ratio = min(max_display_width / w, max_display_height / h)
                    new_width = int(w * ratio)
                    new_height = int(h * ratio)

                    # Redimensionner l'image pour l'affichage
                    preview_rgb = cv2.resize(preview_rgb, (new_width, new_height), interpolation=cv2.INTER_AREA)
                    print(f"📏 Image redimensionnée pour l'affichage: {w}x{h} → {new_width}x{new_height}")

                # Créer un QImage à partir des données de l'image
                qimg = QImage(preview_rgb.data, preview_rgb.shape[1], preview_rgb.shape[0],
                             preview_rgb.strides[0], QImage.Format_RGB888)

                # Créer un QPixmap à partir du QImage
                pixmap = QPixmap.fromImage(qimg)

                # Mettre à jour le label avec mise à l'échelle automatique
                self.image_label.setPixmap(pixmap)
                self.image_label.setAlignment(Qt.AlignCenter)
                self.image_label.setScaledContents(False)  # Garder les proportions
            else:
                # Si la prévisualisation échoue, afficher l'image complète
                frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                qimg = QImage(frame_rgb.data, frame_rgb.shape[1], frame_rgb.shape[0],
                             frame_rgb.strides[0], QImage.Format_RGB888)
                pixmap = QPixmap.fromImage(qimg)
                self.image_label.setPixmap(pixmap)
                self.image_label.setAlignment(Qt.AlignCenter)

        except Exception as e:
            print(f"Erreur lors de la mise à jour de l'image: {e}")
            self.status_bar.showMessage(f"Erreur lors de la mise à jour de l'image: {str(e)}")

    def verify_card_consistency(self, hand_cards, board_cards):
        """Vérifie la cohérence des cartes détectées

        Args:
            hand_cards: Liste des cartes en main
            board_cards: Liste des cartes sur le board

        Returns:
            tuple: (est_cohérent, message_erreur)
        """
        # Vérifier le nombre de cartes
        if len(hand_cards) > 2:
            return False, f"Trop de cartes en main détectées: {len(hand_cards)}/2"

        if len(board_cards) > 5:
            return False, f"Trop de cartes sur le board détectées: {len(board_cards)}/5"

        # Vérifier les doublons
        all_cards = hand_cards + board_cards
        unique_cards = set(all_cards)

        if len(all_cards) != len(unique_cards):
            duplicates = [card for card in all_cards if all_cards.count(card) > 1]
            return False, f"Cartes en double détectées: {', '.join(duplicates)}"

        # Vérifier la validité des valeurs des cartes
        valid_values = ["A", "K", "Q", "J", "10", "9", "8", "7", "6", "5", "4", "3", "2"]
        valid_suits = ["Cœur", "Pique", "Trèfle", "Carreau"]

        for card in all_cards:
            parts = card.split(" de ")
            if len(parts) != 2:
                return False, f"Format de carte invalide: {card}"

            value, suit = parts
            if value not in valid_values:
                return False, f"Valeur de carte invalide: {value} dans {card}"

            if suit not in valid_suits:
                return False, f"Couleur de carte invalide: {suit} dans {card}"

        return True, "Cartes cohérentes"

    def card_to_numeric(self, value):
        """Convertit une valeur de carte en valeur numérique pour les calculs

        Args:
            value: Valeur de la carte (A, K, Q, J, 10, etc.)

        Returns:
            int: Valeur numérique (A=14, K=13, Q=12, etc.)
        """
        value_map = {
            "A": 14,
            "K": 13,
            "Q": 12,
            "J": 11,
            "10": 10,
            "9": 9,
            "8": 8,
            "7": 7,
            "6": 6,
            "5": 5,
            "4": 4,
            "3": 3,
            "2": 2
        }
        return value_map.get(value, 0)

    def evaluate_hand(self, hand_values, hand_suits, board_values, board_suits):
        """Évalue la force d'une main de poker

        Args:
            hand_values: Liste des valeurs des cartes en main
            hand_suits: Liste des couleurs des cartes en main
            board_values: Liste des valeurs des cartes sur le board
            board_suits: Liste des couleurs des cartes sur le board

        Returns:
            tuple: (rang_de_la_main, valeur_de_la_main, description)
                rang_de_la_main: 9=quinte flush royale, 8=quinte flush, 7=carré, etc.
                valeur_de_la_main: valeur numérique pour comparer les mains de même rang
                description: description textuelle de la main
        """
        # Convertir les valeurs en valeurs numériques
        numeric_hand = [self.card_to_numeric(v) for v in hand_values]
        numeric_board = [self.card_to_numeric(v) for v in board_values]

        # Combiner les cartes en main et sur le board
        all_values = numeric_hand + numeric_board
        all_suits = hand_suits + board_suits

        # Trier les valeurs par ordre décroissant
        all_values.sort(reverse=True)

        # Compter les occurrences de chaque valeur
        value_counts = {}
        for value in all_values:
            value_counts[value] = value_counts.get(value, 0) + 1

        # Compter les occurrences de chaque couleur
        suit_counts = {}
        for suit in all_suits:
            suit_counts[suit] = suit_counts.get(suit, 0) + 1

        # Vérifier si on a une couleur (5+ cartes de la même couleur)
        flush_suit = None
        for suit, count in suit_counts.items():
            if count >= 5:
                flush_suit = suit
                break

        # Vérifier si on a une suite (5+ cartes consécutives)
        straight_high = None
        for i in range(len(all_values) - 4):
            if all_values[i] - all_values[i+4] == 4:
                straight_high = all_values[i]
                break

        # Cas spécial: suite A-5 (As compte comme 1)
        if 14 in all_values and 5 in all_values and 4 in all_values and 3 in all_values and 2 in all_values:
            straight_high = 5

        # Vérifier les différentes combinaisons possibles

        # 1. Quinte flush royale (suite à l'As de la même couleur)
        if flush_suit and straight_high == 14:
            # Vérifier que toutes les cartes de la suite sont de la même couleur
            royal_values = [14, 13, 12, 11, 10]
            royal_flush = True
            for i, value in enumerate(all_values):
                if value in royal_values and all_suits[i] != flush_suit:
                    royal_flush = False
                    break
            if royal_flush:
                return 9, 14, "Quinte flush royale"

        # 2. Quinte flush (suite de la même couleur)
        if flush_suit and straight_high:
            # Vérifier que toutes les cartes de la suite sont de la même couleur
            straight_flush = True
            straight_values = list(range(straight_high, straight_high-5, -1))
            for i, value in enumerate(all_values):
                if value in straight_values and all_suits[i] != flush_suit:
                    straight_flush = False
                    break
            if straight_flush:
                return 8, straight_high, f"Quinte flush à {self.numeric_to_card(straight_high)}"

        # 3. Carré (4 cartes de même valeur)
        for value, count in value_counts.items():
            if count == 4:
                return 7, value, f"Carré de {self.numeric_to_card(value)}"

        # 4. Full house (brelan + paire)
        three_of_a_kind = None
        pairs = []
        for value, count in value_counts.items():
            if count == 3:
                if three_of_a_kind is None or value > three_of_a_kind:
                    three_of_a_kind = value
            elif count == 2:
                pairs.append(value)

        if three_of_a_kind and pairs:
            highest_pair = max(pairs)
            return 6, three_of_a_kind * 100 + highest_pair, f"Full {self.numeric_to_card(three_of_a_kind)} par les {self.numeric_to_card(highest_pair)}"

        # 5. Couleur (5+ cartes de la même couleur)
        if flush_suit:
            # Trouver les 5 cartes les plus hautes de la couleur
            flush_values = [all_values[i] for i, suit in enumerate(all_suits) if suit == flush_suit]
            flush_values.sort(reverse=True)
            flush_value = flush_values[0] * 10000 + flush_values[1] * 1000 + flush_values[2] * 100 + flush_values[3] * 10 + flush_values[4]
            return 5, flush_value, f"Couleur {flush_suit} par {self.numeric_to_card(flush_values[0])}"

        # 6. Suite (5 cartes consécutives)
        if straight_high:
            return 4, straight_high, f"Suite à {self.numeric_to_card(straight_high)}"

        # 7. Brelan (3 cartes de même valeur)
        if three_of_a_kind:
            # Trouver les 2 kickers les plus hauts
            kickers = [v for v in all_values if v != three_of_a_kind][:2]
            kicker_value = kickers[0] * 100 + kickers[1]
            return 3, three_of_a_kind * 10000 + kicker_value, f"Brelan de {self.numeric_to_card(three_of_a_kind)}"

        # 8. Deux paires
        if len(pairs) >= 2:
            pairs.sort(reverse=True)
            top_two_pairs = pairs[:2]
            # Trouver le kicker le plus haut
            kickers = [v for v in all_values if v not in top_two_pairs][:1]
            kicker_value = kickers[0] if kickers else 0
            return 2, top_two_pairs[0] * 10000 + top_two_pairs[1] * 100 + kicker_value, f"Deux paires: {self.numeric_to_card(top_two_pairs[0])} et {self.numeric_to_card(top_two_pairs[1])}"

        # 9. Paire
        if pairs:
            pair_value = max(pairs)
            # Trouver les 3 kickers les plus hauts
            kickers = [v for v in all_values if v != pair_value][:3]
            kicker_value = kickers[0] * 10000 + kickers[1] * 100 + kickers[2]
            return 1, pair_value * 1000000 + kicker_value, f"Paire de {self.numeric_to_card(pair_value)}"

        # 10. Carte haute
        if len(all_values) >= 5:
            return 0, all_values[0] * 10000 + all_values[1] * 1000 + all_values[2] * 100 + all_values[3] * 10 + all_values[4], f"Carte haute: {self.numeric_to_card(all_values[0])}"
        elif len(all_values) >= 2:
            # Moins de 5 cartes, utiliser ce qui est disponible
            value = all_values[0] * 10000
            if len(all_values) > 1:
                value += all_values[1] * 1000
            if len(all_values) > 2:
                value += all_values[2] * 100
            if len(all_values) > 3:
                value += all_values[3] * 10
            return 0, value, f"Carte haute: {self.numeric_to_card(all_values[0])}"
        elif len(all_values) == 1:
            return 0, all_values[0] * 10000, f"Carte haute: {self.numeric_to_card(all_values[0])}"
        else:
            return 0, 0, "Aucune carte"

    def numeric_to_card(self, value):
        """Convertit une valeur numérique en valeur de carte

        Args:
            value: Valeur numérique (14=A, 13=K, etc.)

        Returns:
            str: Valeur de la carte (A, K, Q, etc.)
        """
        value_map = {
            14: "As",
            13: "Roi",
            12: "Dame",
            11: "Valet",
            10: "10",
            9: "9",
            8: "8",
            7: "7",
            6: "6",
            5: "5",
            4: "4",
            3: "3",
            2: "2"
        }
        return value_map.get(value, str(value))

    def calculate_hand_equity(self, hand_values, hand_suits, board_values, board_suits):
        """Calcule l'équité de la main (probabilité de gagner)

        Cette fonction utilise une approche simplifiée basée sur la force actuelle de la main
        et le potentiel d'amélioration en fonction des cartes restantes.

        Args:
            hand_values: Liste des valeurs des cartes en main
            hand_suits: Liste des couleurs des cartes en main
            board_values: Liste des valeurs des cartes sur le board
            board_suits: Liste des couleurs des cartes sur le board

        Returns:
            float: Équité estimée (0-100%)
        """
        # Évaluer la force actuelle de la main
        hand_rank, hand_value, hand_desc = self.evaluate_hand(hand_values, hand_suits, board_values, board_suits)

        # Nombre de cartes sur le board
        board_size = len(board_values)

        # Base de l'équité en fonction du rang de la main
        base_equity = {
            9: 95,  # Quinte flush royale
            8: 90,  # Quinte flush
            7: 85,  # Carré
            6: 80,  # Full house
            5: 75,  # Couleur
            4: 65,  # Suite
            3: 55,  # Brelan
            2: 45,  # Deux paires
            1: 35,  # Paire
            0: 25   # Carte haute
        }[hand_rank]

        # Ajuster l'équité en fonction du stade de la partie
        stage_multiplier = {
            0: 1.0,    # Preflop
            1: 1.0,    # 1 carte (situation anormale)
            2: 1.0,    # 2 cartes (situation anormale)
            3: 1.0,    # Flop
            4: 0.9,    # Turn
            5: 0.8     # River
        }.get(board_size, 1.0)  # Valeur par défaut si clé manquante

        # Ajuster l'équité en fonction des outs potentiels
        outs = self.calculate_outs(hand_values, hand_suits, board_values, board_suits, hand_rank)

        # Calculer la probabilité d'amélioration
        cards_to_come = 5 - board_size
        if cards_to_come > 0:
            # Probabilité approximative d'amélioration basée sur le nombre d'outs
            improvement_probability = 1 - ((52 - board_size - 2 - outs) / (52 - board_size - 2)) ** cards_to_come
            improvement_equity = improvement_probability * 30  # Valeur maximale d'amélioration: 30%
        else:
            improvement_equity = 0

        # Équité finale
        equity = (base_equity * stage_multiplier) + improvement_equity

        # Limiter l'équité entre 0 et 100
        return min(max(equity, 0), 100)

    def check_straight_possibility(self, hand_values, hand_suits, board_values, board_suits):
        """Vérifie s'il y a une vraie possibilité de quinte

        Args:
            hand_values: Liste des valeurs des cartes en main
            hand_suits: Liste des couleurs des cartes en main
            board_values: Liste des valeurs des cartes du board
            board_suits: Liste des couleurs des cartes du board

        Returns:
            tuple: (est_possible, description)
        """
        try:
            # Utiliser la logique avancée pour l'analyse des tirages
            draws = poker_integration.advanced_logic.calculate_draws_and_outs(
                hand_values, hand_suits, board_values, board_suits
            )

            # Vérifier s'il y a un tirage de quinte
            if draws['straight_draw']['possible']:
                return (True, draws['straight_draw']['description'])
            else:
                return (False, "Aucune quinte possible")

        except Exception as e:
            print(f"⚠️ Erreur dans check_straight_possibility avec logique avancée: {e}")
            # En cas d'erreur, retourner aucune quinte possible (sécuritaire)
            return (False, "Erreur d'analyse")

    def calculate_outs(self, hand_values, hand_suits, board_values, board_suits, current_rank):
        """Calcule le nombre d'outs (cartes qui améliorent la main)

        Args:
            hand_values: Liste des valeurs des cartes en main
            hand_suits: Liste des couleurs des cartes en main
            board_values: Liste des valeurs des cartes sur le board
            board_suits: Liste des couleurs des cartes sur le board
            current_rank: Rang actuel de la main

        Returns:
            int: Nombre d'outs estimé
        """
        # Convertir les valeurs en valeurs numériques
        numeric_hand = [self.card_to_numeric(v) for v in hand_values]
        numeric_board = [self.card_to_numeric(v) for v in board_values]

        # Toutes les cartes visibles
        all_values = numeric_hand + numeric_board
        all_suits = hand_suits + board_suits

        # Compter les occurrences de chaque valeur et couleur
        value_counts = {}
        for value in all_values:
            value_counts[value] = value_counts.get(value, 0) + 1

        suit_counts = {}
        for suit in all_suits:
            suit_counts[suit] = suit_counts.get(suit, 0) + 1

        # Calculer les outs en fonction de la main actuelle
        outs = 0

        # Outs pour une couleur
        for suit, count in suit_counts.items():
            if count == 4:  # Une carte de plus pour compléter la couleur
                outs += 9  # 13 cartes de la couleur - 4 déjà visibles

        # Outs pour une suite
        # Vérifier les suites potentielles (besoin d'une carte pour compléter)
        straight_outs = 0

        # Créer un set de toutes les cartes déjà sorties (main + board)
        used_cards = set()
        for i, value in enumerate(all_values):
            suit = all_suits[i]
            used_cards.add((value, suit))

        for i in range(2, 15):  # 2 à As
            # Compter combien de cartes consécutives on a dans la fenêtre i-4 à i
            window = list(range(i-4, i+1))
            cards_in_window = sum(1 for v in all_values if v in window)
            if cards_in_window == 4:
                # On a besoin d'une carte pour compléter la suite
                missing_values = [v for v in window if v not in all_values]
                for missing_value in missing_values:
                    # Compter combien de cartes de cette valeur sont encore disponibles
                    available_cards = 0
                    for suit in ['Cœur', 'Pique', 'Trèfle', 'Carreau']:
                        if (missing_value, suit) not in used_cards:
                            available_cards += 1
                    straight_outs += available_cards

        # Outs pour un brelan ou un carré
        for value, count in value_counts.items():
            if count == 2:  # Une paire, besoin de 2 cartes pour un brelan
                outs += 2  # 4 cartes de cette valeur - 2 déjà visibles
            elif count == 3:  # Un brelan, besoin d'une carte pour un carré
                outs += 1  # 4 cartes de cette valeur - 3 déjà visibles

        # Éviter de compter deux fois les mêmes outs
        # Par exemple, une carte peut compléter à la fois une couleur et une suite
        # On prend une estimation conservatrice
        return min(outs, 15)  # Limiter à 15 outs maximum (cas extrême)

    def calculate_pot_odds(self, pot_size, bet_to_call):
        """Calcule les cotes du pot

        Args:
            pot_size: Taille du pot
            bet_to_call: Montant à suivre

        Returns:
            float: Cotes du pot en pourcentage
        """
        if bet_to_call == 0:
            return 0  # Pas besoin de suivre, cotes infinies

        return (bet_to_call / (pot_size + bet_to_call)) * 100

    # ANCIENNE FONCTION calculate_hand_strength SUPPRIMÉE
    # SEULE LA LOGIQUE AVANCÉE EST MAINTENANT UTILISÉE

    def calculate_hand_strength_advanced(self, hand_cards, board_cards, game_results=None):
        """Calcule la force de la main avec la logique avancée et les informations de jeu

        Args:
            hand_cards: Liste des cartes en main au format ["As de Cœur", "Roi de Pique"]
            board_cards: Liste des cartes du board au format ["Dame de Trèfle", ...]
            game_results: Dictionnaire avec les résultats de détection (mises, jetons, etc.)

        Returns:
            tuple: (probability, action, reason)
        """
        # Protection ultra-renforcée contre toutes les erreurs possibles
        try:
            # Vérification de base (logs de debug désactivés pour les performances)
            if not isinstance(hand_cards, list):
                return 50.0, "Checker", "Erreur: hand_cards invalide"

            if not isinstance(board_cards, list):
                return 50.0, "Checker", "Erreur: board_cards invalide"
            # Vérification de disponibilité (obligatoire maintenant)
            if not ADVANCED_POKER_LOGIC_AVAILABLE:
                return 50.0, "Checker", "Erreur: Logique avancée non disponible"

            # Validation des données d'entrée
            if not hand_cards:
                return 50.0, "Checker", "Erreur: Données main vides"

            if len(hand_cards) == 0:
                return 50.0, "Checker", "Erreur: Aucune carte en main"

            print(f"🧠 Analyse avancée - Main: {hand_cards}, Board: {board_cards}")

            # Conversion sécurisée des cartes
            hand_values = []
            hand_suits = []

            try:
                for card in hand_cards:
                    if isinstance(card, str) and " de " in card:
                        parts = card.split(" de ")
                        if len(parts) == 2:
                            value, suit = parts
                            hand_values.append(value)
                            hand_suits.append(suit)
                        else:
                            print(f"⚠️ Format carte main invalide: {card}")
                    else:
                        print(f"⚠️ Carte main non reconnue: {card}")
            except Exception as e:
                print(f"⚠️ Erreur conversion main: {e}")
                return 50.0, "Checker", f"Erreur conversion main: {e}"

            board_values = []
            board_suits = []

            try:
                for card in board_cards:
                    if isinstance(card, str) and " de " in card:
                        parts = card.split(" de ")
                        if len(parts) == 2:
                            value, suit = parts
                            board_values.append(value)
                            board_suits.append(suit)
                        else:
                            print(f"⚠️ Format carte board invalide: {card}")
                    else:
                        print(f"⚠️ Carte board non reconnue: {card}")
            except Exception as e:
                print(f"⚠️ Erreur conversion board: {e}")
                # Continuer avec board vide si erreur
                board_values = []
                board_suits = []

            # Vérification minimale des données
            if not hand_values:
                print("⚠️ Aucune carte main valide")
                return 50.0, "Checker", "Erreur: Aucune carte main valide"

            # Utilisation ultra-sécurisée de la logique avancée avec informations de jeu
            try:
                # Passer les informations de jeu si disponibles
                game_info = game_results if game_results else {}

                analysis_result = poker_integration.evaluate_hand_advanced(
                    hand_values, hand_suits, board_values, board_suits, game_info
                )

                # Validation du résultat
                if not isinstance(analysis_result, dict):
                    print("⚠️ Résultat analyse invalide")
                    return 50.0, "Checker", "Erreur: Résultat analyse invalide"

                # Extraction sécurisée des résultats
                hand_description = analysis_result.get('hand_description', 'Main inconnue')
                equity = analysis_result.get('equity', 50.0)
                draws = analysis_result.get('draws', {})
                recommendations = analysis_result.get('recommendations', {'action': 'check', 'reason': 'Analyse incomplète'})
                game_analysis = analysis_result.get('game_analysis', {})

                # Validation des types
                if not isinstance(equity, (int, float)):
                    equity = 50.0
                if not isinstance(draws, dict):
                    draws = {}
                if not isinstance(recommendations, dict):
                    recommendations = {'action': 'check', 'reason': 'Analyse incomplète'}

                # Construction sécurisée de la description
                try:
                    draw_descriptions = []
                    for draw_type, draw_info in draws.items():
                        if (draw_type not in ['total_outs', 'clean_outs'] and
                            isinstance(draw_info, dict) and
                            draw_info.get('possible', False)):
                            desc = draw_info.get('description', '')
                            if desc:
                                draw_descriptions.append(desc)

                    # Construire la raison complète avec les informations de jeu
                    reason_parts = [hand_description]
                    if draw_descriptions:
                        reason_parts.append(f"Tirages: {', '.join(draw_descriptions)}")
                    reason_parts.append(f"Équité: {equity:.1f}%")

                    # Ajouter les informations de jeu détectées
                    if game_analysis:
                        game_info_parts = []

                        # Afficher les régions détectées
                        detected_regions = game_analysis.get('detected_regions', [])
                        if detected_regions:
                            game_info_parts.append(f"Régions détectées: {len(detected_regions)}")

                        # Afficher la profondeur des tapis avec la moyenne
                        stack_depth = game_analysis.get('stack_depth', 'unknown')
                        avg_stack = game_analysis.get('average_stack', 0)
                        if stack_depth != 'unknown':
                            if avg_stack > 0:
                                game_info_parts.append(f"Tapis {stack_depth} (moy: {avg_stack:.1f} BB)")
                            else:
                                game_info_parts.append(f"Tapis {stack_depth}")

                        # Afficher le bouton détecté
                        button_pos = game_analysis.get('button_position')
                        if button_pos:
                            game_info_parts.append(f"Bouton: {button_pos}")

                        # Afficher les pot odds
                        pot_odds = game_analysis.get('pot_odds', 0)
                        if pot_odds > 0:
                            game_info_parts.append(f"Pot odds: {pot_odds:.1f}%")

                        # Afficher mes jetons et ma mise
                        my_stack = game_analysis.get('my_stack', 0)
                        my_bet = game_analysis.get('my_bet', 0)
                        if my_stack > 0:
                            game_info_parts.append(f"Mes jetons: {my_stack}")
                        if my_bet > 0:
                            game_info_parts.append(f"Ma mise: {my_bet}")

                        if game_info_parts:
                            reason_parts.append(f"Infos jeu: {', '.join(game_info_parts)}")

                    reason_parts.append(recommendations.get('reason', 'Pas de raison'))
                    reason = " - ".join(reason_parts)

                except Exception as e:
                    print(f"⚠️ Erreur construction description: {e}")
                    reason = f"{hand_description} - Équité: {equity:.1f}%"

                # Conversion sécurisée de l'action
                try:
                    action_mapping = {
                        'fold': 'Se coucher',
                        'check': 'Checker',
                        'call': 'Suivre',
                        'bet': 'Miser',
                        'raise': 'Relancer',
                        'bet/raise': 'Miser/Relancer',
                        'bet/call': 'Miser/Suivre',
                        'check/call': 'Checker/Suivre',
                        'call/raise': 'Suivre/Relancer',
                        'all-in': 'All-in'
                    }

                    action_key = recommendations.get('action', 'check')
                    action_french = action_mapping.get(action_key, action_key)

                except Exception as e:
                    print(f"⚠️ Erreur conversion action: {e}")
                    action_french = 'Checker'

                print(f"✅ Analyse avancée terminée - Équité: {equity:.1f}%, Action: {action_french}")

                return float(equity), str(action_french), str(reason)

            except Exception as e:
                print(f"❌ Erreur lors de l'évaluation avancée: {e}")
                import traceback
                traceback.print_exc()
                return 50.0, "Checker", f"Erreur évaluation avancée: {e}"

        except Exception as e:
            print(f"❌ Erreur critique dans l'analyse avancée: {e}")
            import traceback
            traceback.print_exc()
            # Retour par défaut en cas d'erreur totale
            return 50.0, "Checker", f"Erreur critique: {e}"

    def export_realtime_results(self, results):
        """Exporte les résultats en temps réel dans un fichier JSON et un fichier texte

        Args:
            results: Dictionnaire contenant les résultats de la détection
        """
        try:
            print("📊 Début de l'exportation des résultats en temps réel...")

            # Créer un dictionnaire avec les résultats et un horodatage
            export_data = {
                "timestamp": time.time(),
                "datetime": time.strftime("%Y-%m-%d %H:%M:%S"),
                "results": results
            }

            # Chemin du fichier d'exportation JSON
            export_path_json = os.path.join("export", "realtime_results.json")

            # Écrire les résultats dans le fichier JSON
            with open(export_path_json, 'w') as f:
                json.dump(export_data, f, indent=2)

            print(f"✅ Fichier JSON exporté: {export_path_json}")

            # Extraire les cartes en main et sur le board
            hand_cards = []
            board_cards = []

            # Dictionnaire pour normaliser les valeurs des cartes
            card_value_map = {
                # As
                'A': 'As', 'a': 'As', 'AS': 'As', 'as': 'As',
                # Roi
                'R': 'Roi', 'r': 'Roi', 'K': 'Roi', 'k': 'Roi', 'ROI': 'Roi', 'roi': 'Roi',
                # Dame
                'Q': 'Dame', 'q': 'Dame', 'DAME': 'Dame', 'dame': 'Dame', 'D': 'Dame', 'd': 'Dame',
                # Valet
                'J': 'Valet', 'j': 'Valet', 'VALET': 'Valet', 'valet': 'Valet', 'V': 'Valet', 'v': 'Valet',
                # 10
                '10': '10', 'T': '10', 't': '10', 'l0': '10', 'IO': '10', 'io': '10', 'lo': '10', 'LO': '10',
                # 9 - souvent confondu avec g/G
                '9': '9', 'g': '9', 'G': '9',
                # 8 - souvent confondu avec B/b
                '8': '8', 'B': '8', 'b': '8',
                # 7 - souvent confondu avec T/l/L/I/i
                '7': '7', 'T': '7', 'l': '7', 'L': '7', 'I': '7', 'i': '7',
                # 6 - souvent confondu avec G/g/b/B
                '6': '6', 'G': '6', 'g': '6', 'b': '6', 'B': '6',
                # 5 - souvent confondu avec S/s/G/g
                '5': '5', 'S': '5', 's': '5', 'G': '5', 'g': '5',
                # 4 - souvent confondu avec A/a
                '4': '4', 'A': '4', 'a': '4',
                # 3 - souvent confondu avec E/e
                '3': '3', 'E': '3', 'e': '3',
                # 2 - souvent confondu avec Z/z
                '2': '2', 'Z': '2', 'z': '2'
            }

            # Traiter d'abord les cartes en main (préfixe "carte_" ou "hand_card_")
            for name, data in results.items():
                if (name.startswith("carte_") or name.startswith("hand_card_")) and data.get("text"):
                    # Normaliser la valeur de la carte
                    card_text = data['text'].strip()
                    normalized_text = card_value_map.get(card_text, card_text)

                    # Déterminer la couleur
                    card_color = self.get_suit_name(data.get('colors', []))

                    # Créer la chaîne de carte
                    card_str = f"{normalized_text} de {card_color}"

                    # Ajouter à la liste des cartes en main
                    hand_cards.append(card_str)
                    print(f"🃏 Carte en main détectée: {card_str} (original: {card_text}, couleurs: {data.get('colors', [])})")

            # Traiter ensuite les cartes du board (préfixe "card_")
            for name, data in results.items():
                if name.startswith("card_") and not name.startswith("card_hand_") and data.get("text"):
                    # Normaliser la valeur de la carte
                    card_text = data['text'].strip()
                    normalized_text = card_value_map.get(card_text, card_text)

                    # Déterminer la couleur
                    card_color = self.get_suit_name(data.get('colors', []))

                    # Créer la chaîne de carte
                    card_str = f"{normalized_text} de {card_color}"

                    # Ajouter à la liste des cartes du board
                    board_cards.append(card_str)
                    print(f"🃏 Carte du board détectée: {card_str} (original: {card_text}, couleurs: {data.get('colors', [])})")

            # Vérifier la cohérence des cartes
            is_consistent, consistency_message = self.verify_card_consistency(hand_cards, board_cards)

            if not is_consistent:
                print(f"⚠️ {consistency_message}")
                # Ajouter un message d'avertissement dans les résultats
                warning_message = f"Avertissement: {consistency_message}"
            else:
                print("✅ Vérification de cohérence des cartes réussie")
                warning_message = ""

            # Calculer la force de la main et l'action recommandée avec les informations de jeu
            # SEULE LA LOGIQUE AVANCÉE EST UTILISÉE
            probability, action, reason = self.calculate_hand_strength_advanced(hand_cards, board_cards, results)

            # Vérifier si des cartes ont été détectées et si une recommandation a été générée
            if not hand_cards or probability is None or action is None or reason is None:
                print("⚠️ Aucune carte en main détectée ou recommandation impossible, pas d'exportation de recommandation")
                # Formater les résultats au format texte sans recommandation
                current_time = time.strftime("%H:%M:%S")
                text_content = f"""Cartes en main: {', '.join(hand_cards) if hand_cards else 'Non détectées'}
Cartes sur le board: {', '.join(board_cards) if board_cards else 'Non détectées'}
{warning_message}

Dernière mise à jour: {current_time}"""
            else:
                # Si des cartes ont été détectées et une recommandation générée, afficher et exporter les recommandations
                print(f"💪 Force de la main calculée: {probability}%")
                print(f"🎯 Action recommandée: {action}")
                print(f"📝 Raison: {reason}")

                # Formater les résultats au format texte avec recommandation
                current_time = time.strftime("%H:%M:%S")
                text_content = f"""Cartes en main: {', '.join(hand_cards) if hand_cards else 'Non détectées'}
Cartes sur le board: {', '.join(board_cards) if board_cards else 'Non détectées'}
Probabilité de gagner: {probability}%
Action recommandée: {action}
Raison: {reason}
{warning_message}

Dernière mise à jour: {current_time}"""

            # Chemin du fichier d'exportation texte
            export_path_text = os.path.join("export", "realtime_results")
            export_path_text_temp = os.path.join("export", "realtime_results.tmp")

            # Écrire d'abord dans un fichier temporaire pour éviter les problèmes de lecture pendant l'écriture
            with open(export_path_text_temp, 'w', encoding='utf-8') as f:
                f.write(text_content)

            # Puis renommer le fichier temporaire en fichier final (opération atomique)
            try:
                # Sur Windows, il faut d'abord supprimer le fichier existant
                if os.path.exists(export_path_text):
                    os.remove(export_path_text)
                os.rename(export_path_text_temp, export_path_text)
            except Exception as e:
                print(f"⚠️ Erreur lors du renommage du fichier temporaire: {e}")
                # En cas d'erreur, essayer une copie directe
                with open(export_path_text_temp, 'r', encoding='utf-8') as src:
                    with open(export_path_text, 'w', encoding='utf-8') as dst:
                        dst.write(src.read())

            print(f"✅ Fichier texte exporté: {export_path_text}")

            # Créer un fichier de signal pour indiquer que de nouvelles données sont disponibles
            signal_file = os.path.join("export", "update_signal.txt")
            with open(signal_file, 'w') as f:
                f.write(str(time.time()))

            self.status_bar.showMessage(f"Résultats exportés vers {export_path_json} et {export_path_text}")
            print("📊 Exportation des résultats terminée avec succès")

        except Exception as e:
            error_message = f"Erreur lors de l'exportation des résultats: {str(e)}"
            self.status_bar.showMessage(error_message)
            print(f"❌ {error_message}")
            print(f"❌ Détails de l'erreur: {type(e).__name__}")

            # Afficher la trace complète de l'erreur pour le débogage
            import traceback
            print(f"❌ Trace d'erreur complète:\n{traceback.format_exc()}")

    def verify_card_consistency(self, hand_cards, board_cards):
        """Vérifie la cohérence des cartes détectées

        Cette fonction vérifie que:
        1. Il n'y a pas de cartes en double
        2. Le nombre de cartes en main est cohérent (0, 1 ou 2)
        3. Le nombre de cartes sur le board est cohérent (0, 3, 4 ou 5)

        Args:
            hand_cards: Liste des cartes en main
            board_cards: Liste des cartes sur le board

        Returns:
            tuple: (is_consistent, message)
                is_consistent: True si les cartes sont cohérentes, False sinon
                message: Message d'explication si les cartes ne sont pas cohérentes
        """
        # Vérifier le nombre de cartes en main
        if len(hand_cards) > 2:
            return False, f"Trop de cartes en main détectées: {len(hand_cards)} (maximum 2)"

        # Vérifier le nombre de cartes sur le board
        if len(board_cards) > 0 and len(board_cards) not in [3, 4, 5]:
            return False, f"Nombre incohérent de cartes sur le board: {len(board_cards)} (devrait être 0, 3, 4 ou 5)"

        # Vérifier les doublons
        all_cards = hand_cards + board_cards
        if len(all_cards) != len(set(all_cards)):
            # Trouver les doublons
            seen = set()
            duplicates = []
            for card in all_cards:
                if card in seen:
                    duplicates.append(card)
                else:
                    seen.add(card)
            return False, f"Cartes en double détectées: {', '.join(duplicates)}"

        # Vérifier la validité des valeurs de cartes
        valid_values = ['A', 'K', 'Q', 'J', '10', '9', '8', '7', '6', '5', '4', '3', '2']
        valid_suits = ['Cœur', 'Pique', 'Trèfle', 'Carreau']

        for card in all_cards:
            parts = card.split(" de ")
            if len(parts) != 2:
                return False, f"Format de carte invalide: {card}"

            value, suit = parts
            if value not in valid_values:
                return False, f"Valeur de carte invalide: {value} dans {card}"

            if suit not in valid_suits:
                return False, f"Couleur de carte invalide: {suit} dans {card}"

        # Tout est cohérent
        return True, "Cartes cohérentes"

    def get_suit_name(self, colors):
        """Détermine le nom de la couleur en fonction des couleurs détectées

        Args:
            colors: Liste des couleurs détectées

        Returns:
            Le nom de la couleur (Cœur, Pique, Trèfle, Carreau) ou une valeur par défaut
        """
        if not colors:
            return "Pique"  # Valeur par défaut

        # Filtrer la couleur blanche qui est souvent présente pour les chiffres/lettres
        filtered_colors = [c for c in colors if c != 'white']

        # Si après filtrage il ne reste aucune couleur, utiliser les couleurs d'origine
        if not filtered_colors:
            filtered_colors = colors

        # Priorité des couleurs (rouge > vert > bleu > noir)
        if "red" in filtered_colors:
            return "Cœur"
        elif "green" in filtered_colors:
            return "Trèfle"
        elif "blue" in filtered_colors:
            return "Carreau"
        elif "black" in filtered_colors or "gray" in filtered_colors:
            return "Pique"
        elif "white" in filtered_colors:
            # Si seul le blanc est présent, considérer comme un pique par défaut
            return "Pique"
        else:
            return "Pique"  # Valeur par défaut

    def toggle_advisor(self, state):
        """Active ou désactive le conseiller poker"""
        if not POKER_ADVISOR_AVAILABLE:
            return

        self.use_advisor = (state == Qt.Checked)

        # Initialiser le conseiller poker si nécessaire
        if self.use_advisor and self.poker_advisor is None:
            self.poker_advisor = PokerAdvisorLight()

        # Mettre à jour l'interface
        advisor_group = self.findChild(QGroupBox, "Conseiller Poker")
        if advisor_group:
            advisor_group.setVisible(self.use_advisor)

        # Mettre à jour la barre de statut
        if self.use_advisor:
            self.status_bar.showMessage("Conseiller poker activé")
        else:
            self.status_bar.showMessage("Conseiller poker désactivé")

    def refresh_advisor_analysis(self):
        """Rafraîchit l'analyse du conseiller poker"""
        if not self.use_advisor or self.poker_advisor is None or not self.current_results:
            return

        # Analyser les résultats actuels
        self.update_advisor_analysis(self.current_results)

    def show_card_correction_dialog(self):
        """Affiche une boîte de dialogue pour corriger manuellement les cartes détectées"""
        if not self.use_advisor or self.poker_advisor is None or not self.current_results:
            QMessageBox.warning(self, "Avertissement", "Aucun résultat de détection disponible.")
            return

        # Créer une boîte de dialogue
        from PyQt5.QtWidgets import QDialog, QComboBox
        dialog = QDialog(self)
        dialog.setWindowTitle("Modifier les cartes")
        dialog.setMinimumWidth(500)
        dialog.setMinimumHeight(400)

        # Créer le layout principal
        layout = QVBoxLayout(dialog)

        # Ajouter un label d'explication
        info_label = QLabel("Sélectionnez la valeur correcte pour chaque carte mal détectée. Les couleurs seront conservées.")
        info_label.setWordWrap(True)
        layout.addWidget(info_label)

        # Créer un widget de défilement pour les cartes
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_widget = QWidget()
        scroll_layout = QVBoxLayout(scroll_widget)

        # Dictionnaire pour stocker les widgets de correction
        correction_widgets = {}

        # Ajouter les cartes du board
        board_group = QGroupBox("Cartes du board")
        board_layout = QVBoxLayout(board_group)

        for i in range(1, 6):
            region_name = f"card_{i}"
            if region_name in self.current_results:
                # Créer un layout horizontal pour cette carte
                card_layout = QHBoxLayout()

                # Label pour le nom de la région
                card_label = QLabel(f"Carte {i}:")
                card_layout.addWidget(card_label)

                # Combobox pour la valeur corrigée
                value_combo = QComboBox()
                value_combo.addItem("Ne pas corriger", "")  # Option pour ne pas corriger
                value_combo.addItem("Pas de cartes", "NONE")  # Option pour indiquer qu'il n'y a pas de carte
                for value in ["A", "K", "Q", "J", "10", "9", "8", "7", "6", "5", "4", "3", "2"]:
                    value_combo.addItem(value, value)
                card_layout.addWidget(value_combo)

                # Ajouter le layout au groupe
                board_layout.addLayout(card_layout)

                # Stocker les widgets pour récupérer les valeurs plus tard
                correction_widgets[region_name] = value_combo

        scroll_layout.addWidget(board_group)

        # Ajouter les cartes en main
        hand_group = QGroupBox("Cartes en main")
        hand_layout = QVBoxLayout(hand_group)

        # Vérifier les deux formats possibles
        hand_regions = []
        for i in range(1, 3):
            for format_name in [f"carte_{i}m", f"hand_card_{i}"]:
                if format_name in self.current_results:
                    hand_regions.append((i, format_name))

        for i, region_name in hand_regions:
            # Créer un layout horizontal pour cette carte
            card_layout = QHBoxLayout()

            # Label pour le nom de la région
            card_label = QLabel(f"Carte en main {i}:")
            card_layout.addWidget(card_label)

            # Combobox pour la valeur corrigée
            value_combo = QComboBox()
            value_combo.addItem("Ne pas corriger", "")  # Option pour ne pas corriger
            value_combo.addItem("Pas de cartes", "NONE")  # Option pour indiquer qu'il n'y a pas de carte
            for value in ["A", "K", "Q", "J", "10", "9", "8", "7", "6", "5", "4", "3", "2"]:
                value_combo.addItem(value, value)
            card_layout.addWidget(value_combo)

            # Ajouter le layout au groupe
            hand_layout.addLayout(card_layout)

            # Stocker les widgets pour récupérer les valeurs plus tard
            correction_widgets[region_name] = value_combo

        scroll_layout.addWidget(hand_group)

        # Ajouter le widget de défilement au layout principal
        scroll_area.setWidget(scroll_widget)
        layout.addWidget(scroll_area)

        # Ajouter les boutons
        buttons_layout = QHBoxLayout()

        # Bouton pour réinitialiser les sélections
        clear_button = QPushButton("Réinitialiser")
        clear_button.clicked.connect(lambda: self.clear_all_corrections(correction_widgets))
        buttons_layout.addWidget(clear_button)

        # Bouton pour appliquer les modifications
        apply_button = QPushButton("Appliquer")
        apply_button.clicked.connect(lambda: self.apply_corrections(correction_widgets, dialog))
        buttons_layout.addWidget(apply_button)

        # Bouton pour annuler
        cancel_button = QPushButton("Annuler")
        cancel_button.clicked.connect(dialog.reject)
        buttons_layout.addWidget(cancel_button)

        layout.addLayout(buttons_layout)

        # Afficher la boîte de dialogue
        dialog.exec_()

    def clear_all_corrections(self, correction_widgets):
        """Efface toutes les corrections manuelles"""
        # Réinitialiser tous les combobox
        for value_combo in correction_widgets.values():
            value_combo.setCurrentIndex(0)

    def apply_corrections(self, correction_widgets, dialog):
        """Applique les corrections manuelles"""
        # Vérifier si le conseiller est disponible
        if not self.poker_advisor:
            QMessageBox.warning(self, "Avertissement", "Le conseiller poker n'est pas disponible.")
            return

        # Parcourir tous les widgets de correction
        corrections_applied = 0
        for region_name, value_combo in correction_widgets.items():
            # Récupérer la valeur sélectionnée
            value = value_combo.currentData()

            # Si une valeur est spécifiée, appliquer la correction
            if value:
                # Récupérer les données de détection pour l'apprentissage
                region_data = self.current_results.get(region_name, {})
                detected_value = region_data.get("text", "").strip()
                detected_confidence = region_data.get("confidence", 0.0)
                colors = region_data.get("colors", [])

                if value == "NONE":
                    # Option "Pas de cartes" sélectionnée - effacer la carte
                    corrected_value = ""
                    corrected_suit = ""
                else:
                    # Déterminer la couleur de la carte à partir des couleurs détectées
                    suit = None
                    if colors:
                        # Priorité des couleurs (rouge > vert > bleu > noir)
                        if "red" in colors:
                            suit = "Cœur"
                        elif "green" in colors:
                            suit = "Trèfle"
                        elif "blue" in colors:
                            suit = "Carreau"
                        elif "black" in colors or "gray" in colors:
                            suit = "Pique"
                        else:
                            suit = "Pique"  # Valeur par défaut

                    corrected_value = value
                    corrected_suit = suit

                # Appliquer la correction au conseiller poker
                print(f"🔧 Application de la correction: {region_name} = '{corrected_value}' de {corrected_suit}")
                success = self.poker_advisor.set_manual_correction(region_name, corrected_value, corrected_suit)
                print(f"🔧 Résultat de l'application: {success}")

                # Vérifier immédiatement que la correction est enregistrée
                current_corrections = self.poker_advisor.get_manual_corrections()
                print(f"🔧 Corrections après application: {current_corrections}")

                corrections_applied += 1

                # Enregistrer la correction pour l'apprentissage
                if self.learning_system and detected_value != corrected_value:
                    try:
                        # Récupérer l'image de la région pour l'apprentissage
                        image_crop = None
                        if hasattr(self, 'current_image') and self.current_image is not None:
                            # Extraire la région de l'image actuelle
                            if hasattr(self.detector, 'config') and 'roi' in self.detector.config:
                                roi_config = self.detector.config['roi'].get(region_name)
                                if roi_config:
                                    x = roi_config['left']
                                    y = roi_config['top']
                                    w = roi_config['width']
                                    h = roi_config['height']
                                    image_crop = self.current_image[y:y+h, x:x+w]

                        # Contexte pour l'apprentissage
                        import time as time_module
                        context_info = {
                            "colors_detected": colors,
                            "region_type": "board" if region_name.startswith("card_") else "hand",
                            "session_time": time_module.time(),
                            "correction_type": "manual_ui"
                        }

                        # Enregistrer la correction
                        correction_id = self.learning_system.record_correction(
                            region_name=region_name,
                            image_crop=image_crop,
                            detected_value=detected_value,
                            corrected_value=corrected_value,
                            detected_confidence=detected_confidence,
                            context_info=context_info
                        )

                        if correction_id:
                            print(f"📚 Correction enregistrée pour l'apprentissage: {correction_id}")

                    except Exception as e:
                        print(f"⚠️ Erreur lors de l'enregistrement pour l'apprentissage: {e}")

        # Fermer la boîte de dialogue
        dialog.accept()

        # Mettre à jour l'analyse si des corrections ont été appliquées
        if corrections_applied > 0:
            print(f"🔄 {corrections_applied} correction(s) appliquée(s), mise à jour de l'affichage...")

            # Forcer une pause pour s'assurer que les corrections sont bien enregistrées
            from time import sleep
            sleep(0.1)

            # Mettre à jour l'analyse avec un message de confirmation
            self.refresh_advisor_analysis()

            # Message de confirmation dans la console seulement (pas de popup)
            print(f"✅ {corrections_applied} correction(s) appliquée(s) avec succès - Analyse mise à jour")
        else:
            print("ℹ️ Aucune correction appliquée")


    def refresh_advisor_analysis(self):
        """Rafraîchit l'analyse du conseiller poker avec les corrections appliquées"""
        if not self.use_advisor or self.poker_advisor is None or not self.current_results:
            print("⚠️ Impossible de rafraîchir: conseiller ou résultats manquants")
            return

        try:
            print("🔄 Rafraîchissement de l'analyse du conseiller poker...")

            # Afficher les corrections actives pour débogage
            corrections = self.poker_advisor.get_manual_corrections()
            if corrections:
                print(f"📝 Corrections actives: {corrections}")
            else:
                print("📝 Aucune correction active")

            # Vider le cache du conseiller pour forcer une nouvelle analyse
            if hasattr(self.poker_advisor, 'cache'):
                self.poker_advisor.cache.clear()
                print("🗑️ Cache du conseiller vidé")

            # Forcer une nouvelle analyse avec les corrections appliquées
            print("🔍 Lancement de l'analyse avec corrections...")
            analysis, formatted_analysis = self.poker_advisor.analyze_detection_results(self.current_results)

            if analysis and formatted_analysis:
                # Afficher les données analysées pour débogage
                print(f"📊 Board analysé: {analysis.get('board_cards_text', 'N/A')}")
                print(f"📊 Main analysée: {analysis.get('hand_cards_text', 'N/A')}")

                # Mettre à jour l'affichage directement
                old_text = self.advisor_text.toPlainText()
                self.advisor_text.setText(formatted_analysis)
                new_text = self.advisor_text.toPlainText()

                if old_text != new_text:
                    print("✅ Affichage du conseiller mis à jour avec les corrections")
                    print(f"📝 Changement détecté dans l'affichage (longueur: {len(old_text)} → {len(new_text)})")
                else:
                    print("⚠️ Aucun changement détecté dans l'affichage")

                # Forcer la mise à jour de l'interface
                self.advisor_text.update()
                self.advisor_text.repaint()

                # Mettre à jour la barre de statut
                self.status_bar.showMessage("Analyse mise à jour avec les corrections appliquées")
            else:
                print("⚠️ Analyse vide après rafraîchissement")
                self.advisor_text.setText("Analyse impossible après correction. Veuillez relancer la détection.")

            print("✅ Analyse du conseiller poker rafraîchie")
        except Exception as e:
            print(f"❌ Erreur lors du rafraîchissement: {e}")
            import traceback
            print(f"❌ Trace complète: {traceback.format_exc()}")
            self.advisor_text.setText(f"Erreur lors du rafraîchissement de l'analyse: {str(e)}")

    def show_learning_dashboard(self):
        """Affiche le tableau de bord d'apprentissage dans une nouvelle fenêtre"""
        if not self.learning_system:
            QMessageBox.warning(self, "Avertissement", "Le système d'apprentissage n'est pas disponible.")
            return

        try:
            # Importer et exécuter le tableau de bord
            import subprocess
            import sys

            # Lancer le script du tableau de bord dans une nouvelle fenêtre
            dashboard_script = os.path.join(os.path.dirname(__file__), "learning_dashboard.py")

            if os.path.exists(dashboard_script):
                # Lancer dans une nouvelle fenêtre de terminal
                if sys.platform.startswith('win'):
                    # Créer une commande robuste pour Windows
                    cmd = f'start cmd /k "python "{dashboard_script}" && pause"'
                    subprocess.Popen(cmd, shell=True)
                else:
                    subprocess.Popen(['python', dashboard_script])

                self.status_bar.showMessage("Tableau de bord d'apprentissage ouvert dans une nouvelle fenêtre")
            else:
                QMessageBox.warning(self, "Erreur", f"Script du tableau de bord introuvable: {dashboard_script}")

        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'ouverture du tableau de bord: {str(e)}")

    def reset_advisor(self):
        """Réinitialise le conseiller poker en effaçant toutes les corrections manuelles"""
        if not self.use_advisor or self.poker_advisor is None:
            return

        try:
            # Effacer toutes les corrections manuelles
            corrections_count = self.poker_advisor.clear_manual_corrections()

            # Mettre à jour l'analyse avec les résultats actuels
            if self.current_results:
                self.update_advisor_analysis(self.current_results)

            # Mettre à jour la barre de statut
            if corrections_count > 0:
                self.status_bar.showMessage(f"Conseiller poker réinitialisé : {corrections_count} correction(s) effacée(s)")
            else:
                self.status_bar.showMessage("Conseiller poker réinitialisé")

        except Exception as e:
            self.advisor_text.setText(f"Erreur lors de la réinitialisation du conseiller : {str(e)}")
            self.status_bar.showMessage(f"Erreur lors de la réinitialisation du conseiller : {str(e)}")

    def update_advisor_analysis(self, results):
        """Met à jour l'analyse du conseiller poker avec les résultats de détection

        Args:
            results (dict): Résultats de la détection
        """
        if not self.use_advisor or self.poker_advisor is None:
            return

        try:
            # Enrichir les résultats avec les informations des régions cochées
            enriched_results = self.enrich_results_with_selected_regions(results)

            # AJOUTER L'ANALYSE DES INFORMATIONS DE JEU
            print("🎯 Analyse des informations de jeu pour le conseiller...")

            # Importer le module d'intégration poker
            from poker_advisor_integration import PokerAdvisorIntegration
            poker_integration = PokerAdvisorIntegration()

            # Analyser les informations de jeu
            game_analysis = poker_integration.analyze_game_info(enriched_results)

            # Ajouter les informations de jeu aux résultats enrichis
            enriched_results['game_analysis'] = game_analysis

            print(f"✅ Informations de jeu ajoutées: {len(game_analysis.get('detected_regions', []))} régions")

            # Analyser les résultats enrichis avec les informations de jeu
            analysis, formatted_analysis = self.poker_advisor.analyze_detection_results(enriched_results)

            if analysis and formatted_analysis:
                # Mettre à jour l'affichage
                self.advisor_text.setText(formatted_analysis)

                # Activer les boutons
                self.refresh_advisor_button.setEnabled(True)
                self.correct_cards_button.setEnabled(True)
                self.reset_advisor_button.setEnabled(True)

                # Mettre à jour la barre de statut
                regions_count = len(enriched_results)
                selected_count = len(self.get_selected_regions())
                self.status_bar.showMessage(f"Analyse mise à jour: {regions_count}/{selected_count} régions analysées")
            else:
                self.advisor_text.setText("Impossible d'analyser les cartes détectées. Veuillez vérifier que les cartes sont correctement détectées.")
                self.status_bar.showMessage("Analyse du conseiller poker impossible")
        except Exception as e:
            self.advisor_text.setText(f"Erreur lors de l'analyse: {str(e)}")
            self.status_bar.showMessage(f"Erreur du conseiller poker: {str(e)}")

    def enrich_results_with_selected_regions(self, results):
        """Enrichit les résultats avec les informations des régions sélectionnées

        Args:
            results (dict): Résultats de détection actuels

        Returns:
            dict: Résultats enrichis avec toutes les régions cochées
        """
        enriched_results = results.copy()
        selected_regions = self.get_selected_regions()

        print(f"🎯 Enrichissement avec {len(selected_regions)} régions sélectionnées")

        # Ajouter les régions sélectionnées qui ne sont pas dans les résultats
        for region_name in selected_regions:
            if region_name not in enriched_results:
                # Créer une entrée vide pour les régions cochées mais non détectées
                enriched_results[region_name] = {
                    "text": "",
                    "colors": [],
                    "confidence": 0.0,
                    "status": "selected_but_not_detected"
                }
                print(f"📍 Région ajoutée: {region_name} (cochée mais non détectée)")

        # Ajouter des métadonnées sur la configuration
        enriched_results["_metadata"] = {
            "selected_regions": selected_regions,
            "total_selected": len(selected_regions),
            "detected_regions": list(results.keys()),
            "total_detected": len(results)
        }

        return enriched_results

    def update_capture_interval(self, value):
        """Met à jour l'intervalle de capture en temps réel

        Args:
            value (float): Nouvel intervalle en secondes
        """
        self.capture_interval = value
        if self.screen_capture_thread and self.screen_capture_thread.isRunning():
            self.screen_capture_thread.set_interval(value)
        self.status_bar.showMessage(f"Intervalle de capture mis à jour: {value:.1f}s")

    def update_fast_mode(self, state):
        """Met à jour le mode rapide pour la détection

        Args:
            state (int): État de la case à cocher (0=désactivé, 2=activé)
        """
        fast_mode = state == 2  # Qt.Checked = 2
        if self.screen_capture_thread and self.screen_capture_thread.isRunning():
            self.screen_capture_thread.set_fast_mode(fast_mode)

        mode_text = "activé" if fast_mode else "désactivé"
        self.status_bar.showMessage(f"Mode rapide {mode_text}")

    def toggle_monitoring(self):
        """Active ou désactive la surveillance du système"""
        if not MONITORING_AVAILABLE or not self.app_monitor:
            QMessageBox.warning(self, "Surveillance non disponible",
                              "Le système de surveillance n'est pas disponible.")
            return

        if self.app_monitor.monitoring:
            # Arrêter la surveillance
            self.app_monitor.stop_monitoring()
            self.monitoring_toggle_button.setText("Démarrer surveillance")
            self.monitoring_toggle_button.setStyleSheet("background-color: #44FF44; color: white;")
            self.monitoring_text.setPlainText("Surveillance arrêtée.")
            if hasattr(self, 'monitoring_timer'):
                self.monitoring_timer.stop()
        else:
            # Démarrer la surveillance
            self.app_monitor.start_monitoring()
            self.monitoring_toggle_button.setText("Arrêter surveillance")
            self.monitoring_toggle_button.setStyleSheet("background-color: #FF4444; color: white;")
            self.monitoring_text.setPlainText("Surveillance démarrée...\nEn attente des premières données...")
            if hasattr(self, 'monitoring_timer'):
                self.monitoring_timer.start(2000)

    def show_monitoring_summary(self):
        """Affiche un résumé des statistiques de surveillance"""
        if not MONITORING_AVAILABLE or not self.app_monitor:
            return

        summary = self.app_monitor.get_summary()
        QMessageBox.information(self, "Résumé de la surveillance", summary)

    def update_monitoring_display(self):
        """Met à jour l'affichage des statistiques de surveillance"""
        if not MONITORING_AVAILABLE or not self.app_monitor or not self.app_monitor.monitoring:
            return

        try:
            # Obtenir les dernières statistiques
            if self.app_monitor.stats_history:
                latest_stats = self.app_monitor.stats_history[-1]

                # Formater l'affichage
                display_text = (
                    f"🖥️ CPU: {latest_stats['system_cpu']:.1f}%\n"
                    f"💾 RAM: {latest_stats['system_memory_mb']:.0f}MB ({latest_stats['system_memory_percent']:.1f}%)\n"
                    f"🎮 GPU: {latest_stats['gpu_memory_mb']:.0f}MB\n"
                    f"🃏 Processus Poker: {latest_stats['poker_processes_count']} ({latest_stats['poker_memory_mb']:.0f}MB)\n"
                    f"⏰ Dernière mise à jour: {latest_stats['timestamp'].strftime('%H:%M:%S')}"
                )

                # Vérifier les alertes
                alerts = self.app_monitor.check_alerts(latest_stats)
                if alerts:
                    display_text += "\n\n🚨 ALERTES:\n" + "\n".join(alerts)

                self.monitoring_text.setPlainText(display_text)

                # Changer la couleur en fonction des alertes
                if alerts:
                    self.monitoring_text.setStyleSheet("background-color: #1E1E1E; color: #FF6666; font-family: 'Courier New', monospace; font-size: 10px;")
                else:
                    self.monitoring_text.setStyleSheet("background-color: #1E1E1E; color: #00FF00; font-family: 'Courier New', monospace; font-size: 10px;")

        except Exception as e:
            print(f"⚠️ Erreur lors de la mise à jour de l'affichage de surveillance: {e}")

    def closeEvent(self, event):
        """Gère l'événement de fermeture de la fenêtre"""
        # Log de debug pour la fermeture
        if DEBUG_CRASH_AVAILABLE:
            log_debug("🚪 Événement de fermeture de l'application détecté")

        # Arrêter la capture en temps réel si elle est en cours
        if self.screen_capture_thread and self.screen_capture_thread.isRunning():
            if DEBUG_CRASH_AVAILABLE:
                log_debug("🛑 Arrêt du thread de capture d'écran")
            self.screen_capture_thread.stop()

        # Arrêter la surveillance si elle est en cours
        if MONITORING_AVAILABLE and self.app_monitor and self.app_monitor.monitoring:
            print("🛑 Arrêt de la surveillance lors de la fermeture de l'application")
            if DEBUG_CRASH_AVAILABLE:
                log_debug("🛑 Arrêt de la surveillance système")
            self.app_monitor.stop_monitoring()

        # Arrêter le timer de surveillance
        if hasattr(self, 'monitoring_timer'):
            if DEBUG_CRASH_AVAILABLE:
                log_debug("⏰ Arrêt du timer de surveillance")
            self.monitoring_timer.stop()

        if DEBUG_CRASH_AVAILABLE:
            log_debug("✅ Fermeture propre de l'application terminée")

        # Accepter l'événement de fermeture
        event.accept()

def main():
    """Fonction principale"""
    # Analyser les arguments de ligne de commande
    import argparse
    parser = argparse.ArgumentParser(description="Interface graphique pour le module de détection de cartes")
    parser.add_argument("--use-cuda", action="store_true", help="Utiliser CUDA (GPU) si disponible")
    parser.add_argument("--use-advisor", action="store_true", help="Activer le conseiller poker intégré")
    parser.add_argument("--enable-monitoring", action="store_true", help="Activer la surveillance du système")
    args = parser.parse_args()

    # Définir les variables d'environnement en fonction des arguments
    if args.use_cuda:
        os.environ['USE_CUDA'] = '1'
    if args.use_advisor:
        os.environ['USE_POKER_ADVISOR'] = '1'
    if args.enable_monitoring:
        os.environ['ENABLE_MONITORING'] = '1'

    app = QApplication(sys.argv)
    window = DetectorGUI()
    window.show()
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
