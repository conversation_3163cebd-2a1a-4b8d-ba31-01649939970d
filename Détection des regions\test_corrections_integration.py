#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test de l'intégration des corrections avec le système d'apprentissage
====================================================================

Ce script teste que les corrections appliquées dans l'interface
mettent bien à jour l'affichage du conseiller poker et sont
enregistrées dans le système d'apprentissage.

Auteur: Augment Agent
Date: 2025-01-25
"""

import sys
import os

# Ajouter le répertoire parent au chemin Python
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from poker_advisor_light import PokerAdvisorLight
from learning_system import LearningSystem

def test_corrections_integration():
    """Test de l'intégration complète des corrections"""
    
    print("🧪 Test de l'intégration des corrections")
    print("=" * 50)
    
    # Créer les instances
    advisor = PokerAdvisorLight()
    learning_system = LearningSystem("test_integration_data")
    
    print("✅ Conseiller poker et système d'apprentissage initialisés")
    
    # Simuler des résultats de détection
    test_results = {
        "card_1": {"text": "8", "colors": ["black", "white"], "confidence": 0.75},
        "card_2": {"text": "K", "colors": ["red", "white"], "confidence": 0.82},
        "card_3": {"text": "Q", "colors": ["black", "white"], "confidence": 0.65},
        "carte_1m": {"text": "A", "colors": ["red", "white"], "confidence": 0.90},
        "carte_2m": {"text": "J", "colors": ["black", "white"], "confidence": 0.88},
    }
    
    print("\n📊 Résultats de détection simulés:")
    for region, data in test_results.items():
        print(f"  {region}: '{data['text']}' (conf: {data['confidence']})")
    
    # Test 1: Analyse AVANT corrections
    print("\n🔍 Test 1: Analyse AVANT corrections")
    print("-" * 40)
    
    analysis_before, formatted_before = advisor.analyze_detection_results(test_results)
    print(f"Cartes board: {analysis_before.get('board_cards_text', 'N/A')}")
    print(f"Cartes main: {analysis_before.get('hand_cards_text', 'N/A')}")
    
    # Test 2: Appliquer des corrections
    print("\n🛠️ Test 2: Application de corrections")
    print("-" * 40)
    
    corrections = [
        {"region": "card_1", "detected": "8", "corrected": "6"},
        {"region": "card_3", "detected": "Q", "corrected": ""},  # Pas de cartes
        {"region": "carte_1m", "detected": "A", "corrected": "10"},
    ]
    
    for correction in corrections:
        region = correction["region"]
        detected = correction["detected"]
        corrected = correction["corrected"]
        
        # Appliquer la correction au conseiller
        if corrected == "":
            # Pas de cartes
            advisor.set_manual_correction(region, "", "")
            print(f"  ✅ {region}: '{detected}' → 'Pas de cartes'")
        else:
            # Correction normale (garder la couleur détectée)
            region_data = test_results.get(region, {})
            colors = region_data.get("colors", [])
            suit = "Cœur" if "red" in colors else "Pique"  # Simplification
            
            advisor.set_manual_correction(region, corrected, suit)
            print(f"  ✅ {region}: '{detected}' → '{corrected} de {suit}'")
        
        # Enregistrer dans le système d'apprentissage
        learning_system.record_correction(
            region_name=region,
            image_crop=None,  # Pas d'image pour ce test
            detected_value=detected,
            corrected_value=corrected,
            detected_confidence=test_results.get(region, {}).get("confidence", 0.0),
            context_info={"test": True}
        )
    
    # Test 3: Analyse APRÈS corrections
    print("\n🔍 Test 3: Analyse APRÈS corrections")
    print("-" * 40)
    
    analysis_after, formatted_after = advisor.analyze_detection_results(test_results)
    print(f"Cartes board: {analysis_after.get('board_cards_text', 'N/A')}")
    print(f"Cartes main: {analysis_after.get('hand_cards_text', 'N/A')}")
    
    # Test 4: Vérifier les changements
    print("\n📈 Test 4: Comparaison AVANT/APRÈS")
    print("-" * 40)
    
    board_before = analysis_before.get('board_cards_text', '')
    board_after = analysis_after.get('board_cards_text', '')
    hand_before = analysis_before.get('hand_cards_text', '')
    hand_after = analysis_after.get('hand_cards_text', '')
    
    if board_before != board_after:
        print(f"  🔄 Board changé: '{board_before}' → '{board_after}'")
    else:
        print(f"  ⚠️ Board inchangé: '{board_before}'")
    
    if hand_before != hand_after:
        print(f"  🔄 Main changée: '{hand_before}' → '{hand_after}'")
    else:
        print(f"  ⚠️ Main inchangée: '{hand_before}'")
    
    # Test 5: Vérifier les corrections actives
    print("\n📋 Test 5: Corrections actives")
    print("-" * 40)
    
    active_corrections = advisor.get_manual_corrections()
    if active_corrections:
        for region, correction in active_corrections.items():
            value = correction.get("value", "")
            suit = correction.get("suit", "")
            if value:
                print(f"  ✅ {region}: {value} de {suit}")
            else:
                print(f"  ✅ {region}: Pas de cartes")
    else:
        print("  ⚠️ Aucune correction active")
    
    # Test 6: Vérifier l'apprentissage
    print("\n🧠 Test 6: Système d'apprentissage")
    print("-" * 40)
    
    stats = learning_system.get_learning_statistics()
    print(f"  📊 Corrections enregistrées: {stats['total_corrections']}")
    print(f"  🧠 Règles adaptatives: {stats['adaptive_rules_count']}")
    
    if stats['most_common_errors']:
        print(f"  ❌ Erreurs communes:")
        for error, count in stats['most_common_errors'].items():
            print(f"     • {error}: {count} fois")
    
    # Test 7: Suggestions basées sur l'apprentissage
    print("\n💡 Test 7: Suggestions d'apprentissage")
    print("-" * 40)
    
    test_suggestions = [
        {"detected": "8", "confidence": 0.70},
        {"detected": "Q", "confidence": 0.60},
        {"detected": "A", "confidence": 0.85},
    ]
    
    for test in test_suggestions:
        suggestion = learning_system.get_suggested_correction(
            test["detected"], 
            test["confidence"], 
            "card_1"
        )
        
        if suggestion:
            print(f"  💡 '{test['detected']}' (conf: {test['confidence']}) → '{suggestion}'")
        else:
            print(f"  ℹ️ Aucune suggestion pour '{test['detected']}' (conf: {test['confidence']})")
    
    print("\n✅ Test d'intégration terminé avec succès!")
    print("\n📝 Résumé des fonctionnalités testées:")
    print("  ✅ Application des corrections au conseiller")
    print("  ✅ Mise à jour de l'analyse après corrections")
    print("  ✅ Enregistrement dans le système d'apprentissage")
    print("  ✅ Création de règles adaptatives")
    print("  ✅ Suggestions basées sur l'apprentissage")
    print("  ✅ Gestion de l'option 'Pas de cartes'")
    
    return True

if __name__ == "__main__":
    test_corrections_integration()
