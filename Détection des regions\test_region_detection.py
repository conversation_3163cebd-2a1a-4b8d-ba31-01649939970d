#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test de détection des régions de montants
=========================================

Ce script teste si les régions de montants sont correctement identifiées
et utilisent la bonne méthode de détection.

Auteur: Augment Agent
Date: 2025-01-27
"""

import os
import sys
import cv2
import numpy as np
from detector import Detector

def test_region_identification():
    """Teste l'identification des types de régions"""
    print("🧪 TEST D'IDENTIFICATION DES RÉGIONS")
    print("=" * 50)
    
    # Charger la configuration
    config_path = os.path.abspath(os.path.join('..', 'Calibration', 'config', 'poker_advisor_config.json'))
    
    if not os.path.exists(config_path):
        print(f"❌ Configuration non trouvée: {config_path}")
        return
    
    # Initialiser le détecteur
    try:
        detector = Detector(config_path, use_cuda=True)
        print("✅ Détecteur initialisé")
    except Exception as e:
        print(f"❌ Erreur lors de l'initialisation: {e}")
        return
    
    # Lister toutes les régions de la configuration
    if hasattr(detector, 'config') and 'roi' in detector.config:
        regions = detector.config['roi']
        print(f"✅ {len(regions)} régions trouvées dans la configuration")
        print()
        
        # Tester l'identification de chaque région
        card_regions = []
        amount_regions = []
        other_regions = []
        
        for region_name in regions.keys():
            # Utiliser la même logique que dans le détecteur
            is_card_region = region_name.startswith(('card_', 'hand_card_', 'carte_'))
            is_amount_region = (region_name.startswith(('jetons_', 'pot', 'mise_', 'montant_', 'allin_')) or 
                              region_name in ('ma_mise', 'mes_jetons', 'pot_total', 'mon_allin'))
            
            if is_card_region:
                card_regions.append(region_name)
            elif is_amount_region:
                amount_regions.append(region_name)
            else:
                other_regions.append(region_name)
        
        # Afficher les résultats
        print("🃏 RÉGIONS DE CARTES:")
        for region in sorted(card_regions):
            print(f"   ✅ {region}")
        print(f"   Total: {len(card_regions)} régions")
        print()
        
        print("💰 RÉGIONS DE MONTANTS:")
        for region in sorted(amount_regions):
            print(f"   💵 {region}")
        print(f"   Total: {len(amount_regions)} régions")
        print()
        
        print("📋 AUTRES RÉGIONS:")
        for region in sorted(other_regions):
            print(f"   📄 {region}")
        print(f"   Total: {len(other_regions)} régions")
        print()
        
        # Vérifier spécifiquement la région mes_jetons
        if 'mes_jetons' in amount_regions:
            print("✅ SUCCÈS: La région 'mes_jetons' est correctement identifiée comme région de montant!")
        else:
            print("❌ ERREUR: La région 'mes_jetons' n'est PAS identifiée comme région de montant!")
        
        print("=" * 50)
        print("📊 RÉSUMÉ:")
        print(f"   Cartes: {len(card_regions)}")
        print(f"   Montants: {len(amount_regions)}")
        print(f"   Autres: {len(other_regions)}")
        print(f"   Total: {len(regions)}")
        
        # Test avec une image factice pour vérifier le comportement
        print("\n🔍 TEST AVEC IMAGE FACTICE:")
        test_image = np.zeros((100, 300, 3), dtype=np.uint8)
        test_image[:] = (50, 50, 50)  # Fond gris foncé
        
        # Ajouter du texte simulé "42,8 BB"
        cv2.putText(test_image, "42,8 BB", (50, 60), cv2.FONT_HERSHEY_SIMPLEX, 1.5, (255, 255, 255), 2)
        
        # Tester la détection de montant
        try:
            detected_amount = detector.detect_amount_text(test_image)
            print(f"   Montant détecté: '{detected_amount}'")
            if detected_amount and '42' in detected_amount:
                print("   ✅ Détection de montant fonctionne!")
            else:
                print("   ⚠️ Détection de montant à améliorer")
        except Exception as e:
            print(f"   ❌ Erreur lors de la détection: {e}")
        
    else:
        print("❌ Aucune région trouvée dans la configuration")

if __name__ == "__main__":
    test_region_identification()
    input("\nAppuyez sur Entrée pour fermer...")
