#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test des seuils de blanc améliorés
=================================

Ce script teste que les nouveaux seuils de blanc empêchent
les fausses détections sur les tapis avec du blanc faible.

Auteur: Augment Agent
Date: 2025-01-25
"""

import sys
import os
import cv2
import numpy as np

# Ajouter le répertoire parent au chemin Python
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from detector import Detector

def create_test_images():
    """Crée des images de test pour simuler différents niveaux de blanc"""

    test_images = {}

    # 1. Image avec très peu de blanc (tapis de jeu typique)
    # Simuler un tapis vert avec très peu de blanc
    tapis_faible = np.zeros((100, 100, 3), dtype=np.uint8)
    tapis_faible[:, :] = [20, 80, 20]  # Vert foncé
    # Ajouter quelques pixels blancs épars (1-2%)
    for i in range(0, 100, 20):
        for j in range(0, 100, 25):
            tapis_faible[i:i+2, j:j+2] = [200, 200, 200]  # Blanc faible
    test_images["tapis_faible_blanc"] = tapis_faible

    # 2. Image avec blanc moyen (limite)
    # Simuler un tapis avec un peu plus de blanc (3-4%)
    tapis_moyen = np.zeros((100, 100, 3), dtype=np.uint8)
    tapis_moyen[:, :] = [30, 70, 30]  # Vert moyen
    # Ajouter plus de pixels blancs
    for i in range(0, 100, 15):
        for j in range(0, 100, 15):
            tapis_moyen[i:i+3, j:j+3] = [220, 220, 220]  # Blanc moyen
    test_images["tapis_moyen_blanc"] = tapis_moyen

    # 3. Image avec beaucoup de blanc (vraie carte)
    # Simuler une vraie carte avec chiffres/lettres blancs
    vraie_carte = np.zeros((100, 100, 3), dtype=np.uint8)
    vraie_carte[:, :] = [50, 50, 200]  # Fond bleu (carreau)
    # Ajouter un "A" blanc au centre (10-15% de blanc)
    cv2.putText(vraie_carte, "A", (30, 70), cv2.FONT_HERSHEY_SIMPLEX, 2, (255, 255, 255), 3)
    test_images["vraie_carte"] = vraie_carte

    # 4. Image avec blanc très élevé (carte avec beaucoup de blanc)
    # Simuler une carte avec beaucoup d'éléments blancs
    carte_blanche = np.zeros((100, 100, 3), dtype=np.uint8)
    carte_blanche[:, :] = [200, 200, 200]  # Fond blanc
    # Ajouter des éléments noirs
    cv2.putText(carte_blanche, "K", (30, 70), cv2.FONT_HERSHEY_SIMPLEX, 2, (0, 0, 0), 3)
    test_images["carte_blanche"] = carte_blanche

    return test_images

def calculate_white_percentage(image):
    """Calcule le pourcentage de pixels blancs dans une image"""

    # Convertir en HSV
    hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)

    # Créer un masque pour les pixels blancs (même seuils que le détecteur)
    lower_white = np.array([0, 0, 180])
    upper_white = np.array([180, 30, 255])
    white_mask = cv2.inRange(hsv, lower_white, upper_white)

    # Calculer le pourcentage
    white_pixels = cv2.countNonZero(white_mask)
    total_pixels = image.shape[0] * image.shape[1]
    white_percentage = (white_pixels / total_pixels) * 100

    return white_percentage

def test_white_thresholds():
    """Test des seuils de blanc améliorés"""

    print("🔍 Test des seuils de blanc améliorés")
    print("=" * 60)

    # Créer les images de test
    test_images = create_test_images()

    # Initialiser le détecteur
    try:
        detector = Detector()
        print("✅ Détecteur initialisé avec succès")
    except Exception as e:
        print(f"❌ Erreur lors de l'initialisation du détecteur: {e}")
        return False

    print("\n📊 ANALYSE DES POURCENTAGES DE BLANC")
    print("-" * 50)

    # Analyser chaque image de test
    for name, image in test_images.items():
        white_percentage = calculate_white_percentage(image)
        print(f"{name:20s}: {white_percentage:6.2f}% de blanc")

    print("\n🧪 TEST DE DÉTECTION AVEC LES NOUVEAUX SEUILS")
    print("-" * 50)

    # Tester la détection de texte avec les nouveaux seuils
    for name, image in test_images.items():
        print(f"\n🔍 Test de '{name}':")

        white_percentage = calculate_white_percentage(image)
        print(f"   Pourcentage de blanc: {white_percentage:.2f}%")

        # Tester pour carte du board (seuil 5.0%)
        print(f"   Test carte du board (seuil 5.0%):")
        try:
            text_board = detector.detect_text_simple(image, is_hand_card=False)
            if text_board:
                print(f"      ✅ Texte détecté: '{text_board}'")
            else:
                print(f"      ❌ Aucun texte détecté (seuil de blanc non atteint)")
        except Exception as e:
            print(f"      ❌ Erreur: {e}")

        # Tester pour carte en main (seuil 3.0%)
        print(f"   Test carte en main (seuil 3.0%):")
        try:
            text_hand = detector.detect_text_simple(image, is_hand_card=True)
            if text_hand:
                print(f"      ✅ Texte détecté: '{text_hand}'")
            else:
                print(f"      ❌ Aucun texte détecté (seuil de blanc non atteint)")
        except Exception as e:
            print(f"      ❌ Erreur: {e}")

        # Évaluation
        expected_detection = white_percentage >= 3.0  # Au moins pour les cartes en main
        actual_detection_hand = text_hand != ""
        actual_detection_board = text_board != ""

        if name.startswith("tapis"):
            # Pour les tapis, on ne devrait PAS détecter de texte
            if not actual_detection_board and not actual_detection_hand:
                print(f"      🎉 SUCCÈS: Fausse détection évitée sur le tapis")
            else:
                print(f"      ⚠️ ATTENTION: Détection sur le tapis (peut être une fausse détection)")
        else:
            # Pour les vraies cartes, on devrait détecter du texte
            if actual_detection_hand or actual_detection_board:
                print(f"      🎉 SUCCÈS: Vraie carte détectée correctement")
            else:
                print(f"      ⚠️ ATTENTION: Vraie carte non détectée (seuil trop élevé?)")

    print("\n📈 COMPARAISON ANCIENS VS NOUVEAUX SEUILS")
    print("-" * 50)

    # Anciens seuils
    old_threshold_hand = 0.5
    old_threshold_board = 1.0

    # Nouveaux seuils (maximaux absolus)
    new_threshold_hand = 50.0
    new_threshold_board = 60.0

    print(f"Anciens seuils: Main={old_threshold_hand}%, Board={old_threshold_board}%")
    print(f"Nouveaux seuils: Main={new_threshold_hand}%, Board={new_threshold_board}%")
    print(f"Amélioration: Main=+{new_threshold_hand-old_threshold_hand}%, Board=+{new_threshold_board-old_threshold_board}%")

    print("\n🎯 PRÉDICTIONS AVEC LES NOUVEAUX SEUILS")
    print("-" * 50)

    for name, image in test_images.items():
        white_percentage = calculate_white_percentage(image)

        # Prédictions avec anciens seuils
        old_detect_hand = white_percentage >= old_threshold_hand
        old_detect_board = white_percentage >= old_threshold_board

        # Prédictions avec nouveaux seuils
        new_detect_hand = white_percentage >= new_threshold_hand
        new_detect_board = white_percentage >= new_threshold_board

        print(f"{name:20s} ({white_percentage:5.2f}%):")
        print(f"   Anciens seuils: Main={old_detect_hand}, Board={old_detect_board}")
        print(f"   Nouveaux seuils: Main={new_detect_hand}, Board={new_detect_board}")

        if name.startswith("tapis"):
            # Pour les tapis, on veut éviter la détection
            improvement_hand = not new_detect_hand and old_detect_hand
            improvement_board = not new_detect_board and old_detect_board
            if improvement_hand or improvement_board:
                print(f"   🎉 AMÉLIORATION: Fausse détection évitée")
            elif new_detect_hand or new_detect_board:
                print(f"   ⚠️ ATTENTION: Détection toujours possible")
            else:
                print(f"   ✅ OK: Pas de détection (comme souhaité)")
        else:
            # Pour les vraies cartes, on veut garder la détection
            if (new_detect_hand or new_detect_board) and (old_detect_hand or old_detect_board):
                print(f"   ✅ OK: Détection préservée")
            elif not (new_detect_hand or new_detect_board) and (old_detect_hand or old_detect_board):
                print(f"   ⚠️ ATTENTION: Détection perdue (seuil trop élevé)")
            else:
                print(f"   ℹ️ INFO: Pas de changement")

    print("\n💡 RECOMMANDATIONS")
    print("-" * 50)

    # Analyser les résultats et donner des recommandations
    tapis_faible_pct = calculate_white_percentage(test_images["tapis_faible_blanc"])
    tapis_moyen_pct = calculate_white_percentage(test_images["tapis_moyen_blanc"])
    vraie_carte_pct = calculate_white_percentage(test_images["vraie_carte"])

    if tapis_faible_pct < new_threshold_hand and tapis_moyen_pct < new_threshold_board:
        print("✅ Les nouveaux seuils devraient éliminer les fausses détections sur votre tapis")
    elif tapis_faible_pct < new_threshold_hand:
        print("✅ Les nouveaux seuils éliminent les fausses détections les plus courantes")
        print("⚠️ Certaines zones avec plus de blanc pourraient encore causer des fausses détections")
    else:
        print("⚠️ Les seuils pourraient nécessiter un ajustement supplémentaire")

    if vraie_carte_pct >= new_threshold_hand:
        print("✅ Les vraies cartes devraient toujours être détectées")
    else:
        print("⚠️ ATTENTION: Les seuils pourraient être trop élevés et empêcher la détection de vraies cartes")
        print("💡 Considérez réduire légèrement les seuils si vous perdez des détections valides")

    print(f"\n🎯 SEUILS OPTIMAUX SUGGÉRÉS POUR VOTRE TAPIS:")
    print(f"   Cartes en main: {max(2.0, tapis_moyen_pct + 0.5):.1f}%")
    print(f"   Cartes du board: {max(3.0, tapis_moyen_pct + 1.0):.1f}%")

    print("\n✅ Test des seuils de blanc terminé!")
    return True

if __name__ == "__main__":
    test_white_thresholds()
