#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test de la logique avancée de poker
"""

from poker_logic_advanced import AdvancedPokerLogic

def test_hand_evaluation():
    """Test de l'évaluation des mains"""
    print("🃏 TEST D'ÉVALUATION DES MAINS")
    print("=" * 50)

    logic = AdvancedPokerLogic()

    # Test 1: Quinte flush royale
    print("\n🧪 Test 1: Quinte flush royale")
    hand_cards = ["As de Cœur", "Roi de Cœur"]
    board_cards = ["Dame de Cœur", "Valet de Cœur", "10 de Cœur"]

    hand_values, hand_suits, board_values, board_suits, all_cards = logic.parse_cards(hand_cards, board_cards)
    rank, strength, desc = logic.evaluate_made_hand(all_cards)

    print(f"   Main: {hand_cards}")
    print(f"   Board: {board_cards}")
    print(f"   Résultat: {desc} (rang: {rank}, force: {strength})")

    # Test 2: Paire simple
    print("\n🧪 Test 2: Paire simple")
    hand_cards = ["As de Cœur", "As de Pique"]
    board_cards = ["7 de Trèfle", "3 de Carreau", "9 de Cœur"]

    hand_values, hand_suits, board_values, board_suits, all_cards = logic.parse_cards(hand_cards, board_cards)
    rank, strength, desc = logic.evaluate_made_hand(all_cards)

    print(f"   Main: {hand_cards}")
    print(f"   Board: {board_cards}")
    print(f"   Résultat: {desc} (rang: {rank}, force: {strength})")

    # Test 3: Tirage de couleur
    print("\n🧪 Test 3: Tirage de couleur")
    hand_cards = ["As de Cœur", "Roi de Cœur"]
    board_cards = ["7 de Cœur", "3 de Cœur", "9 de Pique"]

    hand_values, hand_suits, board_values, board_suits, all_cards = logic.parse_cards(hand_cards, board_cards)
    draws = logic.calculate_draws_and_outs(hand_values, hand_suits, board_values, board_suits)

    print(f"   Main: {hand_cards}")
    print(f"   Board: {board_cards}")
    print(f"   Tirage de couleur: {draws['flush_draw']}")
    print(f"   Total outs: {draws['total_outs']}")

    # Test 4: Tirage de quinte
    print("\n🧪 Test 4: Tirage de quinte")
    hand_cards = ["As de Cœur", "Roi de Pique"]
    board_cards = ["Dame de Trèfle", "Valet de Carreau", "7 de Cœur"]

    hand_values, hand_suits, board_values, board_suits, all_cards = logic.parse_cards(hand_cards, board_cards)
    draws = logic.calculate_draws_and_outs(hand_values, hand_suits, board_values, board_suits)

    print(f"   Main: {hand_cards}")
    print(f"   Board: {board_cards}")
    print(f"   Tirage de quinte: {draws['straight_draw']}")
    print(f"   Total outs: {draws['total_outs']}")

    # Test 5: Équité preflop
    print("\n🧪 Test 5: Équité preflop")
    hand_cards = ["As de Cœur", "As de Pique"]
    board_cards = []

    hand_values, hand_suits, board_values, board_suits, all_cards = logic.parse_cards(hand_cards, board_cards)
    equity = logic.calculate_equity(hand_values, hand_suits, board_values, board_suits, num_opponents=1)

    print(f"   Main: {hand_cards}")
    print(f"   Board: {board_cards}")
    print(f"   Équité vs 1 adversaire: {equity:.1f}%")

    # Test 6: Équité avec tirages
    print("\n🧪 Test 6: Équité avec tirages")
    hand_cards = ["As de Cœur", "Roi de Cœur"]
    board_cards = ["7 de Cœur", "3 de Cœur", "9 de Pique"]

    hand_values, hand_suits, board_values, board_suits, all_cards = logic.parse_cards(hand_cards, board_cards)
    equity = logic.calculate_equity(hand_values, hand_suits, board_values, board_suits, num_opponents=1)

    print(f"   Main: {hand_cards}")
    print(f"   Board: {board_cards}")
    print(f"   Équité avec tirage de couleur: {equity:.1f}%")

def test_problematic_cases():
    """Test des cas problématiques identifiés"""
    print("\n\n🔍 TEST DES CAS PROBLÉMATIQUES")
    print("=" * 50)

    logic = AdvancedPokerLogic()

    # Test 1: Fausse détection de quinte
    print("\n🧪 Test 1: Cas où l'ancienne logique détectait une fausse quinte")
    hand_cards = ["5 de Cœur", "3 de Pique"]
    board_cards = ["Roi de Trèfle", "Dame de Carreau", "8 de Cœur", "10 de Pique", "9 de Trèfle"]

    hand_values, hand_suits, board_values, board_suits, all_cards = logic.parse_cards(hand_cards, board_cards)
    rank, strength, desc = logic.evaluate_made_hand(all_cards)
    draws = logic.calculate_draws_and_outs(hand_values, hand_suits, board_values, board_suits)

    print(f"   Main: {hand_cards}")
    print(f"   Board: {board_cards}")
    print(f"   Main actuelle: {desc}")
    print(f"   Tirage de quinte possible: {draws['straight_draw']['possible']}")
    print(f"   ✅ Correct: Pas de fausse détection de quinte")

    # Test 2: Détection correcte des tirages de couleur
    print("\n🧪 Test 2: Détection correcte des tirages de couleur")
    hand_cards = ["As de Cœur", "7 de Cœur"]
    board_cards = ["Roi de Cœur", "3 de Cœur", "9 de Pique"]

    hand_values, hand_suits, board_values, board_suits, all_cards = logic.parse_cards(hand_cards, board_cards)
    draws = logic.calculate_draws_and_outs(hand_values, hand_suits, board_values, board_suits)

    print(f"   Main: {hand_cards}")
    print(f"   Board: {board_cards}")
    print(f"   Tirage de couleur: {draws['flush_draw']}")

    if draws['flush_draw']['possible'] and draws['flush_draw']['outs'] == 9:
        print(f"   ✅ Correct: Tirage de couleur détecté avec 9 outs")
    else:
        print(f"   ❌ Erreur: Détection incorrecte")

    # Test 3: Équité réaliste vs équité simpliste
    print("\n🧪 Test 3: Comparaison équité réaliste vs simpliste")

    # Main faible
    hand_cards = ["7 de Cœur", "2 de Pique"]
    board_cards = []

    hand_values, hand_suits, board_values, board_suits, all_cards = logic.parse_cards(hand_cards, board_cards)
    equity = logic.calculate_equity(hand_values, hand_suits, board_values, board_suits, num_opponents=1)

    print(f"   Main faible (7-2o): {equity:.1f}%")

    # Main forte
    hand_cards = ["As de Cœur", "As de Pique"]
    board_cards = []

    hand_values, hand_suits, board_values, board_suits, all_cards = logic.parse_cards(hand_cards, board_cards)
    equity = logic.calculate_equity(hand_values, hand_suits, board_values, board_suits, num_opponents=1)

    print(f"   Main forte (AA): {equity:.1f}%")

    if equity > 80:
        print(f"   ✅ Correct: Équité réaliste pour AA")
    else:
        print(f"   ❌ Erreur: Équité trop faible pour AA")

def main():
    """Fonction principale de test"""
    print("🚀 TESTS DE LA LOGIQUE AVANCÉE DE POKER")
    print("=" * 60)

    try:
        test_hand_evaluation()
        test_problematic_cases()

        print("\n\n🎉 TOUS LES TESTS SONT TERMINÉS!")
        print("La nouvelle logique avancée est prête à remplacer l'ancienne.")

    except Exception as e:
        print(f"\n❌ Erreur lors des tests: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
