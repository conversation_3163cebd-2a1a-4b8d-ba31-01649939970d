#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test de l'interface avec montants
=================================

Ce script teste que l'interface affiche bien les montants détectés
au lieu de "Texte: Aucun".

Auteur: Augment Agent
Date: 2025-01-27
"""

import os
import sys
import cv2
import numpy as np
from detector import Detector

def test_interface_montants():
    """Teste la détection de montants dans l'interface"""
    print("🧪 TEST DE L'INTERFACE AVEC MONTANTS")
    print("=" * 60)
    
    # Charger la configuration
    config_path = os.path.abspath(os.path.join('..', 'Calibration', 'config', 'poker_advisor_config.json'))
    
    if not os.path.exists(config_path):
        print(f"❌ Configuration non trouvée: {config_path}")
        return False
    
    # Initialiser le détecteur
    try:
        detector = Detector(config_path, use_cuda=True)
        print("✅ Détecteur initialisé")
    except Exception as e:
        print(f"❌ Erreur lors de l'initialisation: {e}")
        return False
    
    # Créer une image de test avec du texte simulant des montants
    print("\n🔍 Création d'une image de test avec montants...")
    test_image = np.zeros((1440, 2560, 3), dtype=np.uint8)
    test_image[:] = (50, 50, 50)  # Fond gris foncé
    
    # Simuler des montants dans différentes régions
    test_amounts = {
        "mes_jetons": "58,9 BB",
        "pot": "22,8 BB", 
        "ma_mise": "5,2 BB",
        "jetons_joueur1": "45,3 BB",
        "mise_joueur1": "10,5 BB"
    }
    
    # Ajouter du texte aux positions approximatives (simulé)
    positions = {
        "mes_jetons": (100, 100),
        "pot": (200, 200),
        "ma_mise": (300, 300),
        "jetons_joueur1": (400, 400),
        "mise_joueur1": (500, 500)
    }
    
    for region_name, amount_text in test_amounts.items():
        if region_name in positions:
            x, y = positions[region_name]
            cv2.putText(test_image, amount_text, (x, y), 
                       cv2.FONT_HERSHEY_SIMPLEX, 1.5, (255, 255, 255), 2)
            print(f"   Ajouté '{amount_text}' à la position {x}, {y} pour {region_name}")
    
    # Tester la détection avec process_image_direct
    print("\n🔍 Test de la détection avec process_image_direct...")
    try:
        results = detector.process_image_direct(test_image, fast_mode=True, parallel=False)
        
        if results:
            print(f"✅ Détection réussie: {len(results)} régions traitées")
            
            # Vérifier spécifiquement les régions de montants
            amount_regions_found = 0
            amount_regions_with_text = 0
            
            for region_name, data in results.items():
                # Vérifier si c'est une région de montant
                is_amount_region = (region_name == "mes_jetons" or region_name.startswith("jetons_joueur") or 
                                  region_name == "ma_mise" or region_name.startswith("mise_joueur") or
                                  region_name in ["pot", "pot_total", "montant_call", "montant_relance"] or
                                  region_name.startswith("allin_joueur") or region_name == "mon_allin")
                
                if is_amount_region:
                    amount_regions_found += 1
                    detected_text = data.get('text', '')
                    colors = data.get('colors', [])
                    
                    print(f"💰 Région de montant '{region_name}':")
                    print(f"   Texte détecté: '{detected_text}'")
                    print(f"   Couleurs: {colors}")
                    
                    if detected_text and detected_text.strip():
                        amount_regions_with_text += 1
                        print(f"   ✅ MONTANT DÉTECTÉ!")
                    else:
                        print(f"   ❌ Aucun montant détecté")
                    print()
            
            print(f"📊 RÉSUMÉ:")
            print(f"   Régions de montants trouvées: {amount_regions_found}")
            print(f"   Régions avec texte détecté: {amount_regions_with_text}")
            
            # Simuler l'affichage de l'interface
            print("\n🖥️ SIMULATION DE L'AFFICHAGE INTERFACE:")
            print("-" * 40)
            
            for region_name, data in results.items():
                # Utiliser la même logique que dans detector_gui.py
                detected_text = data.get('text', '')
                
                # Vérifier si c'est une région de montant
                is_amount_region = (region_name == "mes_jetons" or region_name.startswith("jetons_joueur") or 
                                  region_name == "ma_mise" or region_name.startswith("mise_joueur") or
                                  region_name in ["pot", "pot_total", "montant_call", "montant_relance"] or
                                  region_name.startswith("allin_joueur") or region_name == "mon_allin")
                
                if is_amount_region:
                    if detected_text and detected_text != "Détection de montant en cours...":
                        interface_text = f"💰 Montant: {detected_text}"
                        style = "✅ VERT (détecté)"
                    else:
                        interface_text = f"💰 Montant: Aucun détecté"
                        style = "⚠️ ORANGE (non détecté)"
                    
                    print(f"{region_name}: {interface_text} [{style}]")
            
            print("-" * 40)
            
            if amount_regions_with_text > 0:
                print(f"✅ SUCCÈS: {amount_regions_with_text} régions de montants affichent correctement les valeurs!")
                return True
            else:
                print("❌ PROBLÈME: Aucune région de montant n'affiche de valeur")
                return False
                
        else:
            print("❌ Aucun résultat de détection")
            return False
            
    except Exception as e:
        print(f"❌ Erreur lors de la détection: {e}")
        import traceback
        print(f"❌ Trace: {traceback.format_exc()}")
        return False

def test_detection_montant_simple():
    """Test simple de la fonction detect_amount_text"""
    print("\n🧪 TEST SIMPLE DE DÉTECTION DE MONTANT")
    print("=" * 60)
    
    # Charger la configuration
    config_path = os.path.abspath(os.path.join('..', 'Calibration', 'config', 'poker_advisor_config.json'))
    
    try:
        detector = Detector(config_path, use_cuda=True)
        print("✅ Détecteur initialisé")
    except Exception as e:
        print(f"❌ Erreur lors de l'initialisation: {e}")
        return False
    
    # Créer une image simple avec un montant
    test_image = np.zeros((100, 300, 3), dtype=np.uint8)
    test_image[:] = (50, 50, 50)  # Fond gris foncé
    
    # Ajouter du texte "58,9 BB"
    cv2.putText(test_image, "58,9 BB", (50, 60), cv2.FONT_HERSHEY_SIMPLEX, 1.5, (255, 255, 255), 2)
    
    # Tester la détection de montant directement
    try:
        detected_amount = detector.detect_amount_text(test_image)
        print(f"💰 Montant détecté: '{detected_amount}'")
        
        if detected_amount and '58' in detected_amount:
            print("✅ Détection de montant fonctionne!")
            return True
        else:
            print("❌ Détection de montant ne fonctionne pas")
            return False
            
    except Exception as e:
        print(f"❌ Erreur lors de la détection de montant: {e}")
        return False

if __name__ == "__main__":
    print("🚀 DÉMARRAGE DES TESTS D'INTERFACE MONTANTS")
    print()
    
    success1 = test_detection_montant_simple()
    success2 = test_interface_montants()
    
    print("\n" + "=" * 60)
    if success1 and success2:
        print("🎉 TOUS LES TESTS RÉUSSIS!")
        print("L'interface devrait maintenant afficher correctement les montants.")
    else:
        print("❌ CERTAINS TESTS ONT ÉCHOUÉ")
        print("Il y a encore des problèmes avec l'affichage des montants.")
    
    input("\nAppuyez sur Entrée pour fermer...")
