#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test simple du détecteur pour identifier le problème
"""

def test_import():
    """Test d'import du détecteur"""
    print("🔍 Test d'import du détecteur...")
    try:
        from detector import Detector
        print("✅ Import réussi")
        return True
    except Exception as e:
        print(f"❌ Erreur import: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_init():
    """Test d'initialisation du détecteur"""
    print("🔍 Test d'initialisation...")
    try:
        from detector import Detector
        print("🔄 Création du détecteur...")
        detector = Detector(use_cuda=False)
        print("✅ Détecteur créé")
        return detector
    except Exception as e:
        print(f"❌ Erreur initialisation: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_detection(detector):
    """Test de détection simple"""
    print("🔍 Test de détection...")
    try:
        import numpy as np
        import cv2
        
        # Créer une image simple
        img = np.ones((200, 300, 3), dtype=np.uint8) * 255
        cv2.putText(img, "A", (100, 120), cv2.FONT_HERSHEY_SIMPLEX, 4, (0, 0, 0), 6)
        
        print("🔄 Détection en cours...")
        result = detector.detect_text_fast(img)
        print(f"✅ Résultat: '{result}'")
        return result
    except Exception as e:
        print(f"❌ Erreur détection: {e}")
        import traceback
        traceback.print_exc()
        return ""

if __name__ == "__main__":
    print("🔥 TEST SIMPLE DU DÉTECTEUR")
    print("=" * 40)
    
    # Test 1: Import
    if not test_import():
        exit(1)
    
    # Test 2: Initialisation
    detector = test_init()
    if detector is None:
        exit(1)
    
    # Test 3: Détection
    result = test_detection(detector)
    
    if result:
        print(f"\n🎉 SUCCÈS ! Détection: '{result}'")
    else:
        print("\n⚠️ Détection vide mais pas d'erreur")
    
    print("✅ Test terminé")
