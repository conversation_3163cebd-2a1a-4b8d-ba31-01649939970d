#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test complet de toutes les combinaisons de poker avec la logique avancée.
"""

def test_toutes_combinaisons():
    """Test de toutes les combinaisons de poker"""
    print("🃏 TEST COMPLET DE TOUTES LES COMBINAISONS DE POKER")
    print("=" * 70)
    
    try:
        from poker_advisor_integration import poker_integration
        
        # Test de chaque combinaison dans l'ordre de force
        tests = [
            {
                "nom": "1. Quinte Flush Royale",
                "main": ["As", "Roi"],
                "main_suits": ["Cœur", "Cœur"],
                "board": ["Dame", "Valet", "10"],
                "board_suits": ["Cœur", "Cœur", "Cœur"],
                "attendu": "Quinte flush royale",
                "rang_attendu": 9,
                "equite_min": 95
            },
            {
                "nom": "2. <PERSON><PERSON><PERSON>",
                "main": ["9", "8"],
                "main_suits": ["<PERSON><PERSON>", "<PERSON><PERSON>"],
                "board": ["7", "6", "5"],
                "board_suits": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"],
                "attendu": "<PERSON>uinte flush",
                "rang_attendu": 8,
                "equite_min": 85
            },
            {
                "nom": "3. Carré",
                "main": ["As", "As"],
                "main_suits": ["Cœur", "Pique"],
                "board": ["As", "As", "Roi"],
                "board_suits": ["Trèfle", "Carreau", "Cœur"],
                "attendu": "Carré",
                "rang_attendu": 7,
                "equite_min": 80
            },
            {
                "nom": "4. Full House",
                "main": ["Roi", "Roi"],
                "main_suits": ["Cœur", "Pique"],
                "board": ["Roi", "Dame", "Dame"],
                "board_suits": ["Trèfle", "Cœur", "Pique"],
                "attendu": "Full",
                "rang_attendu": 6,
                "equite_min": 75
            },
            {
                "nom": "5. Couleur",
                "main": ["As", "9"],
                "main_suits": ["Cœur", "Cœur"],
                "board": ["7", "5", "3"],
                "board_suits": ["Cœur", "Cœur", "Cœur"],
                "attendu": "Couleur",
                "rang_attendu": 5,
                "equite_min": 65
            },
            {
                "nom": "6. Quinte",
                "main": ["As", "Roi"],
                "main_suits": ["Cœur", "Pique"],
                "board": ["Dame", "Valet", "10"],
                "board_suits": ["Trèfle", "Carreau", "Cœur"],
                "attendu": "Quinte",
                "rang_attendu": 4,
                "equite_min": 55
            },
            {
                "nom": "7. Brelan",
                "main": ["Roi", "Roi"],
                "main_suits": ["Cœur", "Pique"],
                "board": ["Roi", "Dame", "Valet"],
                "board_suits": ["Trèfle", "Cœur", "Pique"],
                "attendu": "Brelan",
                "rang_attendu": 3,
                "equite_min": 45
            },
            {
                "nom": "8. Deux Paires",
                "main": ["Roi", "Dame"],
                "main_suits": ["Cœur", "Pique"],
                "board": ["Roi", "Dame", "Valet"],
                "board_suits": ["Trèfle", "Cœur", "Pique"],
                "attendu": "Deux paires",
                "rang_attendu": 2,
                "equite_min": 35
            },
            {
                "nom": "9. Paire",
                "main": ["Roi", "Dame"],
                "main_suits": ["Cœur", "Pique"],
                "board": ["Roi", "Valet", "10"],
                "board_suits": ["Trèfle", "Cœur", "Pique"],
                "attendu": "Paire",
                "rang_attendu": 1,
                "equite_min": 25
            },
            {
                "nom": "10. Hauteur",
                "main": ["As", "Dame"],
                "main_suits": ["Cœur", "Pique"],
                "board": ["Valet", "9", "7"],
                "board_suits": ["Trèfle", "Cœur", "Pique"],
                "attendu": "Hauteur",
                "rang_attendu": 0,
                "equite_min": 15
            }
        ]
        
        resultats = []
        
        for test in tests:
            print(f"\n🧪 {test['nom']}")
            print(f"   Main: {test['main']} de {test['main_suits']}")
            print(f"   Board: {test['board']} de {test['board_suits']}")
            
            try:
                result = poker_integration.evaluate_hand_advanced(
                    test['main'], test['main_suits'],
                    test['board'], test['board_suits']
                )
                
                main_detectee = result['hand_description']
                rang_detecte = result['hand_rank']
                equite = result['equity']
                action = result['recommendations']['action']
                
                print(f"   ✅ Détecté: {main_detectee}")
                print(f"   📊 Rang: {rang_detecte}, Équité: {equite:.1f}%")
                print(f"   🎯 Action: {action}")
                
                # Vérifications
                success = True
                
                # Vérifier la combinaison
                if test['attendu'].lower() not in main_detectee.lower():
                    print(f"   ❌ ERREUR: Attendu '{test['attendu']}', détecté '{main_detectee}'")
                    success = False
                
                # Vérifier le rang
                if rang_detecte != test['rang_attendu']:
                    print(f"   ❌ ERREUR: Rang attendu {test['rang_attendu']}, détecté {rang_detecte}")
                    success = False
                
                # Vérifier l'équité minimale
                if equite < test['equite_min']:
                    print(f"   ⚠️ ATTENTION: Équité faible {equite:.1f}% (min {test['equite_min']}%)")
                
                if success:
                    print(f"   ✅ {test['nom']} - CORRECT")
                else:
                    print(f"   ❌ {test['nom']} - ERREUR")
                
                resultats.append({
                    'nom': test['nom'],
                    'success': success,
                    'detecte': main_detectee,
                    'rang': rang_detecte,
                    'equite': equite
                })
                
            except Exception as e:
                print(f"   ❌ ERREUR lors du test: {e}")
                resultats.append({
                    'nom': test['nom'],
                    'success': False,
                    'erreur': str(e)
                })
        
        # Résumé final
        print("\n📋 RÉSUMÉ DES TESTS")
        print("=" * 50)
        
        succes = sum(1 for r in resultats if r['success'])
        total = len(resultats)
        
        for resultat in resultats:
            status = "✅" if resultat['success'] else "❌"
            print(f"   {status} {resultat['nom']}")
            if not resultat['success'] and 'erreur' in resultat:
                print(f"      Erreur: {resultat['erreur']}")
        
        print(f"\n🎯 RÉSULTAT GLOBAL: {succes}/{total} tests réussis")
        
        if succes == total:
            print("\n🎉 PARFAIT ! TOUTES LES COMBINAISONS SONT CORRECTEMENT DÉTECTÉES !")
            print("✅ La logique avancée fonctionne parfaitement pour toutes les mains")
            print("✅ Équités réalistes pour chaque combinaison")
            print("✅ Recommandations d'actions appropriées")
            return True
        else:
            print(f"\n⚠️ {total - succes} combinaisons ont des problèmes")
            print("Vérifiez les erreurs ci-dessus")
            return False
        
    except Exception as e:
        print(f"❌ Erreur générale lors des tests: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_cas_speciaux():
    """Test des cas spéciaux de poker"""
    print("\n\n🔍 TEST DES CAS SPÉCIAUX")
    print("=" * 50)
    
    try:
        from poker_advisor_integration import poker_integration
        
        cas_speciaux = [
            {
                "nom": "Quinte blanche (A-5-4-3-2)",
                "main": ["As", "5"],
                "main_suits": ["Cœur", "Pique"],
                "board": ["4", "3", "2"],
                "board_suits": ["Trèfle", "Carreau", "Cœur"],
                "attendu": "Quinte"
            },
            {
                "nom": "Full avec deux brelans",
                "main": ["As", "As"],
                "main_suits": ["Cœur", "Pique"],
                "board": ["As", "Roi", "Roi", "Roi", "Dame"],
                "board_suits": ["Trèfle", "Cœur", "Pique", "Carreau", "Trèfle"],
                "attendu": "Full"
            },
            {
                "nom": "Couleur avec As haut",
                "main": ["As", "2"],
                "main_suits": ["Cœur", "Cœur"],
                "board": ["Roi", "Dame", "Valet"],
                "board_suits": ["Cœur", "Cœur", "Cœur"],
                "attendu": "Couleur"
            },
            {
                "nom": "Pas de combinaison (hauteur As)",
                "main": ["As", "7"],
                "main_suits": ["Cœur", "Pique"],
                "board": ["Roi", "Dame", "Valet"],
                "board_suits": ["Trèfle", "Carreau", "Cœur"],
                "attendu": "Hauteur"
            }
        ]
        
        for cas in cas_speciaux:
            print(f"\n🧪 {cas['nom']}")
            
            try:
                result = poker_integration.evaluate_hand_advanced(
                    cas['main'], cas['main_suits'],
                    cas['board'], cas['board_suits']
                )
                
                main_detectee = result['hand_description']
                print(f"   ✅ Détecté: {main_detectee}")
                
                if cas['attendu'].lower() in main_detectee.lower():
                    print(f"   ✅ CORRECT")
                else:
                    print(f"   ❌ ERREUR: Attendu '{cas['attendu']}', détecté '{main_detectee}'")
                
            except Exception as e:
                print(f"   ❌ ERREUR: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur test cas spéciaux: {e}")
        return False

def main():
    """Fonction principale"""
    print("🃏 VALIDATION COMPLÈTE DE TOUTES LES COMBINAISONS DE POKER")
    print("=" * 80)
    
    # Test principal
    success1 = test_toutes_combinaisons()
    
    # Test des cas spéciaux
    success2 = test_cas_speciaux()
    
    if success1 and success2:
        print("\n🏆 VALIDATION COMPLÈTE RÉUSSIE !")
        print("=" * 50)
        print("✅ Toutes les combinaisons de poker sont correctement détectées")
        print("✅ Tous les cas spéciaux fonctionnent")
        print("✅ Équités réalistes pour chaque situation")
        print("✅ Recommandations d'actions appropriées")
        print("\n🎯 VOTRE CONSEILLER POKER EST PARFAITEMENT FONCTIONNEL !")
    else:
        print("\n⚠️ CERTAINS PROBLÈMES DÉTECTÉS")
        print("Consultez les détails ci-dessus pour les corrections nécessaires")

if __name__ == "__main__":
    main()
