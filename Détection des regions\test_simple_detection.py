#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test simple de détection pour diagnostiquer le problème
"""

import cv2
import numpy as np
import time

def test_easyocr_basic():
    """Test basique d'EasyOCR"""
    print("🔍 Test EasyOCR basique...")
    
    try:
        import easyocr
        
        # Créer une image très simple avec du texte gros et clair
        img = np.ones((300, 400, 3), dtype=np.uint8) * 255  # Fond blanc
        
        # Ajouter du texte très visible
        cv2.putText(img, "A", (150, 200), cv2.FONT_HERSHEY_SIMPLEX, 8, (0, 0, 0), 12)
        
        # Sauvegarder pour vérification
        cv2.imwrite("test_simple.jpg", img)
        print("✅ Image de test créée: test_simple.jpg")
        
        # Initialiser EasyOCR
        print("🔄 Initialisation EasyOCR...")
        reader = easyocr.Reader(['en'], gpu=False, verbose=False)  # CPU d'abord
        
        # Détecter
        print("🔄 Détection en cours...")
        start = time.time()
        results = reader.readtext(img)
        elapsed = time.time() - start
        
        print(f"✅ Détection terminée en {elapsed:.2f}s")
        print(f"✅ Résultats bruts: {results}")
        
        # Analyser les résultats
        if results:
            for i, (bbox, text, confidence) in enumerate(results):
                print(f"  Résultat {i+1}:")
                print(f"    Texte: '{text}'")
                print(f"    Confiance: {confidence:.3f}")
                print(f"    Bbox: {bbox}")
        else:
            print("⚠️ Aucun texte détecté")
        
        return results
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()
        return []

def test_easyocr_gpu():
    """Test EasyOCR avec GPU"""
    print("\n🔍 Test EasyOCR avec GPU...")
    
    try:
        import easyocr
        import torch
        
        if not torch.cuda.is_available():
            print("⚠️ CUDA non disponible, test ignoré")
            return []
        
        # Créer une image simple
        img = np.ones((300, 400, 3), dtype=np.uint8) * 255
        cv2.putText(img, "K", (150, 200), cv2.FONT_HERSHEY_SIMPLEX, 8, (0, 0, 0), 12)
        
        # Initialiser EasyOCR avec GPU
        print("🔄 Initialisation EasyOCR GPU...")
        reader = easyocr.Reader(['en'], gpu=True, verbose=False)
        
        # Détecter
        print("🔄 Détection GPU en cours...")
        start = time.time()
        results = reader.readtext(img)
        elapsed = time.time() - start
        
        print(f"✅ Détection GPU terminée en {elapsed:.2f}s")
        print(f"✅ Résultats GPU: {results}")
        
        return results
        
    except Exception as e:
        print(f"❌ Erreur GPU: {e}")
        import traceback
        traceback.print_exc()
        return []

def test_preprocessing():
    """Test des différents prétraitements"""
    print("\n🔍 Test des prétraitements...")
    
    try:
        import easyocr
        
        # Image originale
        img = np.ones((200, 300, 3), dtype=np.uint8) * 255
        cv2.putText(img, "Q", (100, 120), cv2.FONT_HERSHEY_SIMPLEX, 4, (0, 0, 0), 6)
        
        reader = easyocr.Reader(['en'], gpu=False, verbose=False)
        
        # Test 1: Image originale
        print("🔄 Test image originale...")
        results1 = reader.readtext(img)
        print(f"  Résultats originaux: {results1}")
        
        # Test 2: Image en niveaux de gris
        print("🔄 Test niveaux de gris...")
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        gray_bgr = cv2.cvtColor(gray, cv2.COLOR_GRAY2BGR)
        results2 = reader.readtext(gray_bgr)
        print(f"  Résultats gris: {results2}")
        
        # Test 3: Image binarisée
        print("🔄 Test binarisation...")
        _, binary = cv2.threshold(gray, 127, 255, cv2.THRESH_BINARY)
        binary_bgr = cv2.cvtColor(binary, cv2.COLOR_GRAY2BGR)
        results3 = reader.readtext(binary_bgr)
        print(f"  Résultats binaires: {results3}")
        
        # Test 4: Image agrandie
        print("🔄 Test agrandissement...")
        enlarged = cv2.resize(img, (0, 0), fx=2, fy=2, interpolation=cv2.INTER_CUBIC)
        results4 = reader.readtext(enlarged)
        print(f"  Résultats agrandis: {results4}")
        
        return [results1, results2, results3, results4]
        
    except Exception as e:
        print(f"❌ Erreur prétraitement: {e}")
        return []

def main():
    print("🔥 TEST SIMPLE DE DÉTECTION")
    print("=" * 40)
    
    # Test 1: EasyOCR basique
    results_cpu = test_easyocr_basic()
    
    # Test 2: EasyOCR GPU
    results_gpu = test_easyocr_gpu()
    
    # Test 3: Prétraitements
    preprocessing_results = test_preprocessing()
    
    # Résumé
    print("\n" + "=" * 40)
    print("📊 RÉSUMÉ")
    print("=" * 40)
    
    cpu_works = bool(results_cpu)
    gpu_works = bool(results_gpu)
    
    print(f"EasyOCR CPU: {'✅ Fonctionne' if cpu_works else '❌ Ne fonctionne pas'}")
    print(f"EasyOCR GPU: {'✅ Fonctionne' if gpu_works else '❌ Ne fonctionne pas'}")
    
    if preprocessing_results:
        working_methods = sum(1 for r in preprocessing_results if r)
        print(f"Prétraitements: {working_methods}/4 méthodes fonctionnent")
    
    if not cpu_works and not gpu_works:
        print("\n❌ PROBLÈME MAJEUR: EasyOCR ne détecte rien")
        print("💡 Causes possibles:")
        print("  - Installation EasyOCR corrompue")
        print("  - Modèles EasyOCR manquants")
        print("  - Problème de configuration")
        print("\n🔧 Solutions:")
        print("  pip uninstall easyocr")
        print("  pip install easyocr")
    elif cpu_works and not gpu_works:
        print("\n⚠️ EasyOCR CPU fonctionne mais pas GPU")
        print("💡 Utiliser CPU temporairement")
    else:
        print("\n✅ EasyOCR fonctionne correctement")

if __name__ == "__main__":
    main()
