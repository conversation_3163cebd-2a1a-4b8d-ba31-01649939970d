#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Script de test pour vérifier la stabilité de l'application après les améliorations
de gestion de la mémoire GPU et des erreurs.
"""

import os
import sys
import time
import psutil
import gc
from detector import Detector

def monitor_memory():
    """Surveille l'utilisation de la mémoire"""
    process = psutil.Process()
    memory_info = process.memory_info()
    return {
        'rss': memory_info.rss / 1024 / 1024,  # MB
        'vms': memory_info.vms / 1024 / 1024,  # MB
        'percent': process.memory_percent()
    }

def monitor_gpu_memory():
    """Surveille l'utilisation de la mémoire GPU"""
    try:
        import torch
        if torch.cuda.is_available():
            allocated = torch.cuda.memory_allocated(0) / 1024 / 1024  # MB
            cached = torch.cuda.memory_reserved(0) / 1024 / 1024  # MB
            return {'allocated': allocated, 'cached': cached}
    except ImportError:
        pass
    return {'allocated': 0, 'cached': 0}

def test_detector_stability(num_iterations=50):
    """Test la stabilité du détecteur sur plusieurs itérations"""
    print("🧪 Test de stabilité du détecteur")
    print(f"📊 Nombre d'itérations: {num_iterations}")
    print("=" * 60)
    
    # Initialiser le détecteur
    config_path = os.path.abspath(os.path.join('..', 'Calibration', 'config', 'poker_advisor_config.json'))
    
    if not os.path.exists(config_path):
        print(f"❌ Fichier de configuration non trouvé: {config_path}")
        return False
    
    try:
        detector = Detector(config_path)
        print(f"✅ Détecteur initialisé avec: {config_path}")
    except Exception as e:
        print(f"❌ Erreur lors de l'initialisation du détecteur: {e}")
        return False
    
    # Créer une image de test simple
    import cv2
    import numpy as np
    test_image = np.zeros((600, 800, 3), dtype=np.uint8)
    cv2.putText(test_image, "TEST", (300, 300), cv2.FONT_HERSHEY_SIMPLEX, 2, (255, 255, 255), 3)
    
    # Statistiques
    success_count = 0
    error_count = 0
    memory_stats = []
    gpu_stats = []
    
    print("🚀 Début du test de stabilité...")
    start_time = time.time()
    
    for i in range(num_iterations):
        try:
            # Surveiller la mémoire avant
            mem_before = monitor_memory()
            gpu_before = monitor_gpu_memory()
            
            # Exécuter la détection
            results = detector.process_image_direct(test_image, fast_mode=True, parallel=False)
            
            # Surveiller la mémoire après
            mem_after = monitor_memory()
            gpu_after = monitor_gpu_memory()
            
            # Vérifier les résultats
            if results is not None:
                success_count += 1
                print(f"✅ Itération {i+1}/{num_iterations} - Succès")
            else:
                error_count += 1
                print(f"❌ Itération {i+1}/{num_iterations} - Échec (résultats None)")
            
            # Enregistrer les statistiques mémoire
            memory_stats.append({
                'iteration': i+1,
                'mem_before': mem_before,
                'mem_after': mem_after,
                'gpu_before': gpu_before,
                'gpu_after': gpu_after
            })
            
            # Afficher les stats mémoire tous les 10 itérations
            if (i + 1) % 10 == 0:
                print(f"📊 Mémoire RAM: {mem_after['rss']:.1f} MB ({mem_after['percent']:.1f}%)")
                print(f"📊 Mémoire GPU: {gpu_after['allocated']:.1f} MB allouée, {gpu_after['cached']:.1f} MB en cache")
                
                # Vérifier les fuites mémoire
                if i > 0:
                    mem_growth = mem_after['rss'] - memory_stats[0]['mem_after']['rss']
                    gpu_growth = gpu_after['allocated'] - memory_stats[0]['gpu_after']['allocated']
                    
                    if mem_growth > 100:  # Plus de 100 MB de croissance
                        print(f"⚠️ Possible fuite mémoire RAM: +{mem_growth:.1f} MB")
                    
                    if gpu_growth > 50:  # Plus de 50 MB de croissance GPU
                        print(f"⚠️ Possible fuite mémoire GPU: +{gpu_growth:.1f} MB")
            
            # Petite pause pour éviter la surcharge
            time.sleep(0.1)
            
        except Exception as e:
            error_count += 1
            print(f"❌ Itération {i+1}/{num_iterations} - Erreur: {e}")
            
            # Nettoyage en cas d'erreur
            try:
                if hasattr(detector, '_cleanup_gpu_memory'):
                    detector._cleanup_gpu_memory()
            except:
                pass
    
    # Statistiques finales
    end_time = time.time()
    duration = end_time - start_time
    
    print("=" * 60)
    print("📈 RÉSULTATS DU TEST DE STABILITÉ")
    print("=" * 60)
    print(f"⏱️  Durée totale: {duration:.2f} secondes")
    print(f"✅ Succès: {success_count}/{num_iterations} ({success_count/num_iterations*100:.1f}%)")
    print(f"❌ Erreurs: {error_count}/{num_iterations} ({error_count/num_iterations*100:.1f}%)")
    
    if memory_stats:
        initial_mem = memory_stats[0]['mem_after']['rss']
        final_mem = memory_stats[-1]['mem_after']['rss']
        mem_growth = final_mem - initial_mem
        
        initial_gpu = memory_stats[0]['gpu_after']['allocated']
        final_gpu = memory_stats[-1]['gpu_after']['allocated']
        gpu_growth = final_gpu - initial_gpu
        
        print(f"🧠 Mémoire RAM: {initial_mem:.1f} MB → {final_mem:.1f} MB (Δ{mem_growth:+.1f} MB)")
        print(f"🎮 Mémoire GPU: {initial_gpu:.1f} MB → {final_gpu:.1f} MB (Δ{gpu_growth:+.1f} MB)")
        
        # Évaluation de la stabilité
        if error_count == 0:
            print("🎉 EXCELLENT: Aucune erreur détectée!")
        elif error_count < num_iterations * 0.05:  # Moins de 5% d'erreurs
            print("✅ BON: Très peu d'erreurs détectées")
        elif error_count < num_iterations * 0.1:  # Moins de 10% d'erreurs
            print("⚠️ MOYEN: Quelques erreurs détectées")
        else:
            print("❌ PROBLÉMATIQUE: Trop d'erreurs détectées")
        
        if abs(mem_growth) < 50:  # Moins de 50 MB de croissance
            print("🧠 EXCELLENT: Mémoire RAM stable")
        elif abs(mem_growth) < 100:
            print("⚠️ MOYEN: Légère croissance de la mémoire RAM")
        else:
            print("❌ PROBLÉMATIQUE: Fuite mémoire RAM détectée")
        
        if abs(gpu_growth) < 20:  # Moins de 20 MB de croissance GPU
            print("🎮 EXCELLENT: Mémoire GPU stable")
        elif abs(gpu_growth) < 50:
            print("⚠️ MOYEN: Légère croissance de la mémoire GPU")
        else:
            print("❌ PROBLÉMATIQUE: Fuite mémoire GPU détectée")
    
    return error_count == 0 and abs(mem_growth) < 100 and abs(gpu_growth) < 50

def main():
    """Fonction principale"""
    print("🔧 Test de stabilité de l'application Poker Advisor")
    print("=" * 60)
    
    # Test avec différents nombres d'itérations
    test_cases = [10, 25, 50]
    
    for num_iter in test_cases:
        print(f"\n🧪 Test avec {num_iter} itérations")
        success = test_detector_stability(num_iter)
        
        if success:
            print(f"✅ Test {num_iter} itérations: RÉUSSI")
        else:
            print(f"❌ Test {num_iter} itérations: ÉCHEC")
        
        # Nettoyage entre les tests
        gc.collect()
        time.sleep(2)
    
    print("\n🏁 Tests de stabilité terminés!")

if __name__ == "__main__":
    main()
