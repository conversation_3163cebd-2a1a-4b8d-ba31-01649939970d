#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test du détecteur restauré
"""

import cv2
import numpy as np
import time

def test_detector_basic():
    """Test basique du détecteur"""
    print("🔍 Test du détecteur restauré...")
    
    try:
        from detector import Detector
        
        print("🔄 Initialisation du détecteur...")
        detector = Detector(use_cuda=False)  # CPU pour stabilité
        
        print("🔄 Création image de test...")
        # Créer une image de test simple
        test_image = np.ones((200, 300, 3), dtype=np.uint8) * 255
        cv2.putText(test_image, "A", (100, 120), cv2.FONT_HERSHEY_SIMPLEX, 4, (0, 0, 0), 6)
        
        print("🔄 Test de détection...")
        start_time = time.time()
        result = detector.detect_text_fast(test_image)
        detection_time = time.time() - start_time
        
        print(f"✅ Détection terminée en {detection_time:.2f}s")
        print(f"✅ Résultat: '{result}'")
        
        # Test de couleur
        print("🔄 Test de détection de couleur...")
        colors = detector.detect_colors_fast(test_image)
        print(f"✅ Couleurs détectées: {colors}")
        
        return len(result) > 0
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🔥 TEST DU DÉTECTEUR RESTAURÉ")
    print("=" * 40)
    
    success = test_detector_basic()
    
    if success:
        print("\n✅ Le détecteur fonctionne à nouveau !")
        print("💡 Votre application devrait maintenant détecter les cartes.")
    else:
        print("\n❌ Le détecteur ne fonctionne toujours pas")
        print("💡 Vérifiez l'installation de PaddleOCR ou EasyOCR")
