#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test de détection des montants avec virgules
============================================

Ce script teste la nouvelle fonctionnalité de détection des montants
qui prend en compte les virgules et les points décimaux.

Auteur: Augment Agent
Date: 2025-01-27
"""

import os
import sys
import cv2
import numpy as np
from detector import Detector

def create_test_amount_image(amount_text):
    """Crée une image de test avec un montant spécifique"""
    # Créer une image de test
    image = np.zeros((60, 200, 3), dtype=np.uint8)
    
    # Fond sombre (comme dans l'interface de poker)
    image[:] = (40, 40, 40)
    
    # Ajouter le texte du montant en blanc/jaune
    font = cv2.FONT_HERSHEY_SIMPLEX
    font_scale = 1.2
    color = (255, 255, 255)  # Blanc
    thickness = 2
    
    # Calculer la position pour centrer le texte
    text_size = cv2.getTextSize(amount_text, font, font_scale, thickness)[0]
    x = (image.shape[1] - text_size[0]) // 2
    y = (image.shape[0] + text_size[1]) // 2
    
    cv2.putText(image, amount_text, (x, y), font, font_scale, color, thickness)
    
    return image

def test_amount_detection():
    """Teste la détection de différents formats de montants"""
    print("🧪 TEST DE DÉTECTION DES MONTANTS")
    print("=" * 50)
    
    # Charger la configuration
    config_path = os.path.abspath(os.path.join('..', 'Calibration', 'config', 'poker_advisor_config.json'))
    
    if not os.path.exists(config_path):
        print(f"❌ Configuration non trouvée: {config_path}")
        return
    
    # Initialiser le détecteur
    try:
        detector = Detector(config_path, use_cuda=True)
        print("✅ Détecteur initialisé")
    except Exception as e:
        print(f"❌ Erreur lors de l'initialisation: {e}")
        return
    
    # Tests avec différents formats de montants
    test_cases = [
        "42,8",      # Format français avec virgule
        "42.8",      # Format anglais avec point
        "1,234.56",  # Format avec séparateur de milliers
        "1234,56",   # Format français avec milliers
        "100",       # Nombre entier
        "0,5",       # Moins d'une unité
        "999,99",    # Presque 1000
        "42,8 BB",   # Avec unité BB
        "1.5K",      # Avec unité K
        "25M",       # Avec unité M
    ]
    
    print(f"🔍 Test de {len(test_cases)} formats de montants...")
    print()
    
    results = []
    
    for i, amount_text in enumerate(test_cases, 1):
        print(f"Test {i:2d}: '{amount_text}'")
        print("-" * 30)
        
        # Créer l'image de test
        test_image = create_test_amount_image(amount_text)
        
        # Sauvegarder l'image pour débogage
        debug_path = f"debug_amount_{i:02d}_{amount_text.replace(',', '_').replace('.', '_').replace(' ', '_')}.png"
        cv2.imwrite(debug_path, test_image)
        print(f"   Image sauvée: {debug_path}")
        
        # Tester la détection
        try:
            detected = detector.detect_amount_text(test_image)
            
            if detected:
                success = True
                # Vérifier si la détection est correcte
                # Nettoyer les deux chaînes pour la comparaison
                original_clean = amount_text.replace(' BB', '').replace('K', '').replace('M', '')
                detected_clean = detected.replace(' ', '')
                
                if original_clean == detected_clean:
                    status = "✅ PARFAIT"
                elif original_clean.replace(',', '.') == detected_clean.replace(',', '.'):
                    status = "✅ CORRECT (format différent)"
                else:
                    status = "⚠️ PARTIEL"
                    success = False
            else:
                status = "❌ ÉCHEC"
                success = False
                detected = "(rien détecté)"
            
            print(f"   Résultat: {detected}")
            print(f"   Statut: {status}")
            
            results.append({
                'input': amount_text,
                'output': detected,
                'success': success,
                'status': status
            })
            
        except Exception as e:
            print(f"   ❌ Erreur: {e}")
            results.append({
                'input': amount_text,
                'output': f"ERREUR: {e}",
                'success': False,
                'status': "❌ ERREUR"
            })
        
        print()
    
    # Résumé des résultats
    print("=" * 50)
    print("📊 RÉSUMÉ DES RÉSULTATS")
    print("=" * 50)
    
    successful = sum(1 for r in results if r['success'])
    total = len(results)
    success_rate = (successful / total) * 100 if total > 0 else 0
    
    print(f"Réussis: {successful}/{total} ({success_rate:.1f}%)")
    print()
    
    print("📋 DÉTAIL DES RÉSULTATS:")
    print(f"{'Entrée':<12} {'Sortie':<12} {'Statut':<20}")
    print("-" * 50)
    
    for result in results:
        print(f"{result['input']:<12} {result['output']:<12} {result['status']:<20}")
    
    print()
    
    if success_rate >= 80:
        print("🎉 Excellent ! La détection des montants fonctionne bien.")
    elif success_rate >= 60:
        print("👍 Bon ! La détection fonctionne mais peut être améliorée.")
    else:
        print("⚠️ La détection nécessite des améliorations.")
    
    print(f"\n✅ Test terminé ! Taux de réussite: {success_rate:.1f}%")

if __name__ == "__main__":
    test_amount_detection()
    input("\nAppuyez sur Entrée pour fermer...")
