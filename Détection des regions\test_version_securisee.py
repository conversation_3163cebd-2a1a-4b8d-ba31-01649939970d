#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test de la version sécurisée de l'intégration de la logique avancée.
"""

import sys
import os

def test_version_securisee():
    """Test de la version sécurisée sans interface graphique"""
    print("🛡️ TEST DE LA VERSION SÉCURISÉE")
    print("=" * 50)
    
    try:
        # Simuler la classe DetectorGUI sans interface
        class MockDetectorGUI:
            def __init__(self):
                # Importer la logique avancée
                try:
                    from poker_advisor_integration import poker_integration
                    self.ADVANCED_POKER_LOGIC_AVAILABLE = True
                    print("✅ Logique avancée importée")
                except ImportError:
                    self.ADVANCED_POKER_LOGIC_AVAILABLE = False
                    print("❌ Logique avancée non disponible")
            
            def calculate_hand_strength(self, hand_cards, board_cards):
                """Ancienne logique (fallback)"""
                return 50.0, "Checker", "Ancienne logique utilisée"
            
            def calculate_hand_strength_advanced(self, hand_cards, board_cards):
                """Version sécurisée de la logique avancée"""
                # Copier exactement le code de detector_gui.py
                ADVANCED_POKER_LOGIC_AVAILABLE = self.ADVANCED_POKER_LOGIC_AVAILABLE
                
                # Protection contre les erreurs critiques
                try:
                    # Vérification de disponibilité
                    if not ADVANCED_POKER_LOGIC_AVAILABLE:
                        print("⚠️ Logique avancée non disponible, utilisation de l'ancienne logique")
                        return self.calculate_hand_strength(hand_cards, board_cards)

                    # Validation des données d'entrée
                    if not hand_cards or not isinstance(hand_cards, list):
                        print("⚠️ Données main invalides, utilisation de l'ancienne logique")
                        return self.calculate_hand_strength(hand_cards, board_cards)

                    if not isinstance(board_cards, list):
                        print("⚠️ Données board invalides, utilisation de l'ancienne logique")
                        return self.calculate_hand_strength(hand_cards, board_cards)

                    print(f"🧠 Analyse avancée - Main: {hand_cards}, Board: {board_cards}")

                    # Conversion sécurisée des cartes
                    hand_values = []
                    hand_suits = []

                    try:
                        for card in hand_cards:
                            if isinstance(card, str) and " de " in card:
                                parts = card.split(" de ")
                                if len(parts) == 2:
                                    value, suit = parts
                                    hand_values.append(value)
                                    hand_suits.append(suit)
                                else:
                                    print(f"⚠️ Format carte main invalide: {card}")
                            else:
                                print(f"⚠️ Carte main non reconnue: {card}")
                    except Exception as e:
                        print(f"⚠️ Erreur conversion main: {e}")
                        return self.calculate_hand_strength(hand_cards, board_cards)

                    board_values = []
                    board_suits = []

                    try:
                        for card in board_cards:
                            if isinstance(card, str) and " de " in card:
                                parts = card.split(" de ")
                                if len(parts) == 2:
                                    value, suit = parts
                                    board_values.append(value)
                                    board_suits.append(suit)
                                else:
                                    print(f"⚠️ Format carte board invalide: {card}")
                            else:
                                print(f"⚠️ Carte board non reconnue: {card}")
                    except Exception as e:
                        print(f"⚠️ Erreur conversion board: {e}")
                        # Continuer avec board vide si erreur
                        board_values = []
                        board_suits = []

                    # Vérification minimale des données
                    if not hand_values:
                        print("⚠️ Aucune carte main valide, utilisation de l'ancienne logique")
                        return self.calculate_hand_strength(hand_cards, board_cards)

                    # Utilisation sécurisée de la logique avancée
                    try:
                        from poker_advisor_integration import poker_integration
                        
                        analysis_result = poker_integration.evaluate_hand_advanced(
                            hand_values, hand_suits, board_values, board_suits
                        )

                        # Validation du résultat
                        if not isinstance(analysis_result, dict):
                            print("⚠️ Résultat analyse invalide, utilisation de l'ancienne logique")
                            return self.calculate_hand_strength(hand_cards, board_cards)

                        # Extraction sécurisée des résultats
                        hand_description = analysis_result.get('hand_description', 'Main inconnue')
                        equity = analysis_result.get('equity', 50.0)
                        draws = analysis_result.get('draws', {})
                        recommendations = analysis_result.get('recommendations', {'action': 'check', 'reason': 'Analyse incomplète'})

                        # Validation des types
                        if not isinstance(equity, (int, float)):
                            equity = 50.0
                        if not isinstance(draws, dict):
                            draws = {}
                        if not isinstance(recommendations, dict):
                            recommendations = {'action': 'check', 'reason': 'Analyse incomplète'}

                        # Construction sécurisée de la description
                        try:
                            draw_descriptions = []
                            for draw_type, draw_info in draws.items():
                                if (draw_type not in ['total_outs', 'clean_outs'] and 
                                    isinstance(draw_info, dict) and 
                                    draw_info.get('possible', False)):
                                    desc = draw_info.get('description', '')
                                    if desc:
                                        draw_descriptions.append(desc)

                            # Construire la raison complète
                            reason_parts = [hand_description]
                            if draw_descriptions:
                                reason_parts.append(f"Tirages: {', '.join(draw_descriptions)}")
                            reason_parts.append(f"Équité: {equity:.1f}%")
                            reason_parts.append(recommendations.get('reason', 'Pas de raison'))

                            reason = " - ".join(reason_parts)

                        except Exception as e:
                            print(f"⚠️ Erreur construction description: {e}")
                            reason = f"{hand_description} - Équité: {equity:.1f}%"

                        # Conversion sécurisée de l'action
                        try:
                            action_mapping = {
                                'fold': 'Se coucher',
                                'check': 'Checker',
                                'call': 'Suivre',
                                'bet': 'Miser',
                                'raise': 'Relancer',
                                'bet/raise': 'Miser/Relancer',
                                'bet/call': 'Miser/Suivre',
                                'check/call': 'Checker/Suivre',
                                'call/raise': 'Suivre/Relancer',
                                'all-in': 'All-in'
                            }

                            action_key = recommendations.get('action', 'check')
                            action_french = action_mapping.get(action_key, action_key)

                        except Exception as e:
                            print(f"⚠️ Erreur conversion action: {e}")
                            action_french = 'Checker'

                        print(f"✅ Analyse avancée terminée - Équité: {equity:.1f}%, Action: {action_french}")

                        return float(equity), str(action_french), str(reason)

                    except Exception as e:
                        print(f"❌ Erreur lors de l'évaluation avancée: {e}")
                        import traceback
                        traceback.print_exc()
                        return self.calculate_hand_strength(hand_cards, board_cards)

                except Exception as e:
                    print(f"❌ Erreur critique dans l'analyse avancée: {e}")
                    import traceback
                    traceback.print_exc()
                    # En cas d'erreur critique, utiliser l'ancienne logique
                    try:
                        return self.calculate_hand_strength(hand_cards, board_cards)
                    except Exception as e2:
                        print(f"❌ Erreur aussi dans l'ancienne logique: {e2}")
                        # Retour par défaut en cas d'erreur totale
                        return 50.0, "Checker", "Erreur d'analyse - Action par défaut"
        
        # Créer une instance de test
        detector = MockDetectorGUI()
        
        # Test 1: Main normale
        print("\n🧪 Test 1: Main normale")
        hand_cards = ["As de Cœur", "Roi de Pique"]
        board_cards = ["Dame de Trèfle", "Valet de Carreau", "10 de Cœur"]
        
        equity, action, reason = detector.calculate_hand_strength_advanced(hand_cards, board_cards)
        print(f"   Résultat: {equity:.1f}% - {action} - {reason}")
        
        # Test 2: Données invalides
        print("\n🧪 Test 2: Données invalides")
        hand_cards = None
        board_cards = ["Dame de Trèfle"]
        
        equity, action, reason = detector.calculate_hand_strength_advanced(hand_cards, board_cards)
        print(f"   Résultat: {equity:.1f}% - {action} - {reason}")
        
        # Test 3: Format carte invalide
        print("\n🧪 Test 3: Format carte invalide")
        hand_cards = ["As_Cœur", "Roi de Pique"]  # Format invalide
        board_cards = []
        
        equity, action, reason = detector.calculate_hand_strength_advanced(hand_cards, board_cards)
        print(f"   Résultat: {equity:.1f}% - {action} - {reason}")
        
        # Test 4: Main preflop
        print("\n🧪 Test 4: Main preflop")
        hand_cards = ["As de Cœur", "As de Pique"]
        board_cards = []
        
        equity, action, reason = detector.calculate_hand_strength_advanced(hand_cards, board_cards)
        print(f"   Résultat: {equity:.1f}% - {action} - {reason}")
        
        print("\n✅ TOUS LES TESTS SÉCURISÉS SONT RÉUSSIS!")
        return True
        
    except Exception as e:
        print(f"\n❌ Erreur lors des tests: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Fonction principale"""
    print("🛡️ TESTS DE LA VERSION SÉCURISÉE DE LA LOGIQUE AVANCÉE")
    print("=" * 70)
    
    if test_version_securisee():
        print("\n🎉 LA VERSION SÉCURISÉE FONCTIONNE PARFAITEMENT!")
        print("\nVous pouvez maintenant lancer l'application principale:")
        print("   python detector_gui.py")
        print("\nLa logique avancée est maintenant protégée contre les erreurs.")
    else:
        print("\n❌ PROBLÈME AVEC LA VERSION SÉCURISÉE")

if __name__ == "__main__":
    main()
