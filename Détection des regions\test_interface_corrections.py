#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test des corrections d'interface
===============================

Ce script simule l'application des corrections dans l'interface
pour vérifier que l'affichage se met bien à jour.

Auteur: Augment Agent
Date: 2025-01-25
"""

import sys
import os

# Ajouter le répertoire parent au chemin Python
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from poker_advisor_light import PokerAdvisorLight

def simulate_interface_corrections():
    """Simule les corrections d'interface"""
    
    print("🧪 Test des corrections d'interface")
    print("=" * 50)
    
    # C<PERSON>er une instance du conseiller
    advisor = PokerAdvisorLight()
    
    # Simuler des résultats de détection réalistes
    test_results = {
        "card_1": {"text": "K", "colors": ["blue", "white"], "confidence": 0.85},
        "card_2": {"text": "Q", "colors": ["black", "white"], "confidence": 0.82},
        "card_3": {"text": "2", "colors": ["black", "white"], "confidence": 0.78},
        "carte_1m": {"text": "J", "colors": ["blue", "white"], "confidence": 0.90},
        "carte_2m": {"text": "7", "colors": ["red", "white"], "confidence": 0.88},
    }
    
    print("📊 Résultats de détection simulés:")
    for region, data in test_results.items():
        print(f"  {region}: '{data['text']}' {data['colors']} (conf: {data['confidence']})")
    
    # Test 1: Analyse AVANT corrections
    print("\n🔍 Test 1: Analyse AVANT corrections")
    print("-" * 40)
    
    try:
        analysis_before, formatted_before = advisor.analyze_detection_results(test_results)
        print("✅ Analyse AVANT réussie")
        print(f"Board: {analysis_before.get('board_cards_text', 'N/A')}")
        print(f"Main: {analysis_before.get('hand_cards_text', 'N/A')}")
        print(f"Recommandation: {analysis_before.get('recommendation', 'N/A')}")
    except Exception as e:
        print(f"❌ Erreur dans l'analyse AVANT: {e}")
        return False
    
    # Test 2: Appliquer des corrections (simulation de l'interface)
    print("\n🛠️ Test 2: Application de corrections")
    print("-" * 40)
    
    corrections = [
        {"region": "card_1", "detected": "K", "corrected": "A", "suit": "Carreau"},
        {"region": "card_3", "detected": "2", "corrected": "", "suit": ""},  # Pas de cartes
    ]
    
    for correction in corrections:
        region = correction["region"]
        detected = correction["detected"]
        corrected = correction["corrected"]
        suit = correction["suit"]
        
        # Appliquer la correction
        success = advisor.set_manual_correction(region, corrected, suit)
        
        if corrected == "":
            print(f"  ✅ {region}: '{detected}' → 'Pas de cartes' (succès: {success})")
        else:
            print(f"  ✅ {region}: '{detected}' → '{corrected} de {suit}' (succès: {success})")
    
    # Test 3: Vider le cache (simulation de refresh_advisor_analysis)
    print("\n🗑️ Test 3: Vidage du cache")
    print("-" * 40)
    
    if hasattr(advisor, 'cache'):
        advisor.cache.clear()
        print("✅ Cache vidé")
    else:
        print("ℹ️ Pas de cache à vider")
    
    # Test 4: Analyse APRÈS corrections
    print("\n🔍 Test 4: Analyse APRÈS corrections")
    print("-" * 40)
    
    try:
        analysis_after, formatted_after = advisor.analyze_detection_results(test_results)
        print("✅ Analyse APRÈS réussie")
        print(f"Board: {analysis_after.get('board_cards_text', 'N/A')}")
        print(f"Main: {analysis_after.get('hand_cards_text', 'N/A')}")
        print(f"Recommandation: {analysis_after.get('recommendation', 'N/A')}")
    except Exception as e:
        print(f"❌ Erreur dans l'analyse APRÈS: {e}")
        import traceback
        print(f"❌ Trace: {traceback.format_exc()}")
        return False
    
    # Test 5: Comparaison des résultats
    print("\n📈 Test 5: Comparaison AVANT/APRÈS")
    print("-" * 40)
    
    board_before = analysis_before.get('board_cards_text', '')
    board_after = analysis_after.get('board_cards_text', '')
    hand_before = analysis_before.get('hand_cards_text', '')
    hand_after = analysis_after.get('hand_cards_text', '')
    
    if board_before != board_after:
        print(f"  🔄 Board changé: '{board_before}' → '{board_after}' ✅")
    else:
        print(f"  ⚠️ Board inchangé: '{board_before}'")
    
    if hand_before != hand_after:
        print(f"  🔄 Main changée: '{hand_before}' → '{hand_after}' ✅")
    else:
        print(f"  ℹ️ Main inchangée: '{hand_before}' (normal si pas de correction)")
    
    # Test 6: Affichage formaté
    print("\n📝 Test 6: Affichage formaté APRÈS corrections")
    print("-" * 40)
    
    print("Analyse formatée:")
    print(formatted_after)
    
    print("\n✅ Test des corrections d'interface terminé avec succès!")
    print("\n📝 Résumé:")
    print("  ✅ Analyse AVANT corrections")
    print("  ✅ Application des corrections")
    print("  ✅ Vidage du cache")
    print("  ✅ Analyse APRÈS corrections")
    print("  ✅ Comparaison des résultats")
    print("  ✅ Affichage formaté")
    
    return True

if __name__ == "__main__":
    simulate_interface_corrections()
