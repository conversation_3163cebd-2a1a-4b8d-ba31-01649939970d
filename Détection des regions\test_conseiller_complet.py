#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test complet du conseiller poker
===============================

Ce script teste que le conseiller poker utilise bien toutes les données
de capture et affiche correctement les informations détectées.

Auteur: Augment Agent
Date: 2025-01-27
"""

import os
import sys
from poker_advisor_light import PokerAdvisorLight

def test_conseiller_complet():
    """Teste le conseiller poker avec des données complètes"""
    print("🧪 TEST COMPLET DU CONSEILLER POKER")
    print("=" * 60)
    
    # Créer une instance du conseiller
    advisor = PokerAdvisorLight()
    
    # Simuler des résultats de détection complets avec vos régions
    test_results = {
        # Cartes du board
        "card_1": {"text": "A", "colors": ["red", "white"], "confidence": 0.90},
        "card_2": {"text": "K", "colors": ["black", "white"], "confidence": 0.88},
        "card_3": {"text": "Q", "colors": ["red", "white"], "confidence": 0.85},
        
        # Cartes en main
        "carte_1m": {"text": "J", "colors": ["black", "white"], "confidence": 0.92},
        "carte_2m": {"text": "10", "colors": ["red", "white"], "confidence": 0.87},
        
        # Mes jetons et mise
        "mes_jetons": {"text": "58.9", "colors": ["white"], "confidence": 0.95},
        "ma_mise": {"text": "5.2", "colors": ["white"], "confidence": 0.90},
        
        # Pot
        "pot": {"text": "22.8", "colors": ["white"], "confidence": 0.93},
        
        # Jetons des autres joueurs
        "jetons_joueur1": {"text": "45.3", "colors": ["white"], "confidence": 0.88},
        "jetons_joueur2": {"text": "67.1", "colors": ["white"], "confidence": 0.91},
        
        # Mises des autres joueurs
        "mise_joueur1": {"text": "5.2", "colors": ["white"], "confidence": 0.89},
        "mise_joueur2": {"text": "10.5", "colors": ["white"], "confidence": 0.86},
        
        # Montants d'action
        "montant_call": {"text": "10.5", "colors": ["white"], "confidence": 0.92},
        "montant_relance": {"text": "25.0", "colors": ["white"], "confidence": 0.88},
    }
    
    print("📊 Résultats de détection simulés:")
    print("🃏 CARTES:")
    print(f"   Board: A♥ K♠ Q♥")
    print(f"   Main: J♠ 10♥")
    print("💰 MONTANTS:")
    print(f"   Mes jetons: 58.9 BB")
    print(f"   Ma mise: 5.2 BB")
    print(f"   Pot: 22.8 BB")
    print(f"   Joueur 1: 45.3 BB (mise: 5.2 BB)")
    print(f"   Joueur 2: 67.1 BB (mise: 10.5 BB)")
    print(f"   Call: 10.5 BB, Relance: 25.0 BB")
    print()
    
    # Analyser les résultats
    print("🔍 ANALYSE DU CONSEILLER:")
    print("-" * 40)
    
    try:
        analysis, formatted_analysis = advisor.analyze_detection_results(test_results)
        
        print("✅ Analyse réussie!")
        print()
        
        # Vérifier que toutes les données sont bien extraites
        print("📋 DONNÉES EXTRAITES:")
        print(f"   Board détecté: {analysis.get('board_cards_text', 'N/A')}")
        print(f"   Main détectée: {analysis.get('hand_cards_text', 'N/A')}")
        print(f"   Mes jetons: {analysis.get('my_stack', 'N/A')} BB")
        print(f"   Ma mise: {analysis.get('my_bet', 'N/A')} BB")
        print(f"   Pot: {analysis.get('pot', 'N/A')} BB")
        print(f"   Pot total: {analysis.get('pot_total', 'N/A')} BB")
        print(f"   Jetons adverses: {analysis.get('player_stacks', {})}")
        print(f"   Mises adverses: {analysis.get('player_bets', {})}")
        print(f"   Montant call: {analysis.get('call_amount', 'N/A')} BB")
        print(f"   Montant relance: {analysis.get('raise_amount', 'N/A')} BB")
        print(f"   Régions détectées: {len(analysis.get('detected_regions', []))}")
        print()
        
        # Vérifier l'analyse poker
        print("🎯 ANALYSE POKER:")
        poker_analysis = analysis.get('poker_analysis', {})
        print(f"   Force de la main: {poker_analysis.get('hand_strength', 'N/A')}")
        print(f"   Équité: {poker_analysis.get('equity', 'N/A')}")
        print(f"   Pot odds: {poker_analysis.get('pot_odds', 'N/A')}%")
        print(f"   Action recommandée: {poker_analysis.get('recommended_action', 'N/A')}")
        print(f"   Raison: {poker_analysis.get('action_reason', 'N/A')}")
        print()
        
        # Afficher l'analyse formatée
        print("📄 ANALYSE FORMATÉE:")
        print("-" * 40)
        print(formatted_analysis)
        print("-" * 40)
        
        # Vérifier les indicateurs de détection
        print("🔍 INDICATEURS DE DÉTECTION:")
        detected_chips = poker_analysis.get('detected_chips', False)
        detected_bets = poker_analysis.get('detected_bets', False)
        detected_pot = poker_analysis.get('detected_pot', False)
        
        print(f"   Jetons détectés: {'✅' if detected_chips else '❌'}")
        print(f"   Mises détectées: {'✅' if detected_bets else '❌'}")
        print(f"   Pot détecté: {'✅' if detected_pot else '❌'}")
        
        # Vérifier que les montants avec virgules sont bien traités
        if analysis.get('my_stack') == 58.9:
            print("✅ Montants avec virgules correctement détectés!")
        else:
            print(f"⚠️ Problème avec les montants: {analysis.get('my_stack')} au lieu de 58.9")
        
        print()
        print("=" * 60)
        print("✅ TEST COMPLET RÉUSSI!")
        print("Le conseiller utilise bien toutes les données de capture.")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors de l'analyse: {e}")
        import traceback
        print(f"❌ Trace: {traceback.format_exc()}")
        return False

def test_conseiller_avec_donnees_partielles():
    """Teste le conseiller avec des données partielles"""
    print("\n🧪 TEST AVEC DONNÉES PARTIELLES")
    print("=" * 60)
    
    advisor = PokerAdvisorLight()
    
    # Simuler des résultats partiels (seulement quelques régions)
    test_results_partial = {
        # Seulement mes jetons et une carte
        "mes_jetons": {"text": "42.8", "colors": ["white"], "confidence": 0.95},
        "carte_1m": {"text": "A", "colors": ["red", "white"], "confidence": 0.90},
    }
    
    print("📊 Résultats partiels simulés:")
    print(f"   Mes jetons: 42.8 BB")
    print(f"   Une carte en main: A♥")
    print()
    
    try:
        analysis, formatted_analysis = advisor.analyze_detection_results(test_results_partial)
        
        print("✅ Analyse avec données partielles réussie!")
        print()
        print("📄 ANALYSE FORMATÉE:")
        print("-" * 40)
        print(formatted_analysis)
        print("-" * 40)
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors de l'analyse partielle: {e}")
        return False

if __name__ == "__main__":
    success1 = test_conseiller_complet()
    success2 = test_conseiller_avec_donnees_partielles()
    
    if success1 and success2:
        print("\n🎉 TOUS LES TESTS RÉUSSIS!")
        print("Le conseiller poker fonctionne parfaitement avec vos données de capture.")
    else:
        print("\n❌ CERTAINS TESTS ONT ÉCHOUÉ")
    
    input("\nAppuyez sur Entrée pour fermer...")
