#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test de l'OCR amélioré pour les montants
=======================================

Ce script teste la nouvelle détection OCR améliorée qui devrait
détecter "28,9" au lieu de seulement "9".

Auteur: Augment Agent
Date: 2025-01-27
"""

import os
import sys
import cv2
import numpy as np
from detector import Detector

def test_ocr_ameliore():
    """Teste l'OCR amélioré avec différents montants"""
    print("🧪 TEST DE L'OCR AMÉLIORÉ POUR LES MONTANTS")
    print("=" * 60)

    # Charger la configuration
    config_path = os.path.abspath(os.path.join('..', 'Calibration', 'config', 'poker_advisor_config.json'))

    try:
        detector = Detector(config_path, use_cuda=True)
        print("✅ Détecteur initialisé")
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

    # Créer des images de test avec différents montants
    test_cases = [
        {"amount": "21.1", "description": "Nouveau cas problématique: 21.1 → 2"},
        {"amount": "28,9", "description": "Cas problématique principal"},
        {"amount": "10,8", "description": "Cas précédent"},
        {"amount": "42,5", "description": "Autre montant décimal"},
        {"amount": "156,2", "description": "Montant plus grand"},
        {"amount": "5,0", "description": "Montant avec zéro"}
    ]

    print("\n🔍 TESTS DE DÉTECTION OCR AMÉLIORÉE:")
    print("-" * 50)

    success_count = 0
    for i, test_case in enumerate(test_cases, 1):
        amount = test_case["amount"]
        description = test_case["description"]

        print(f"\n📋 Test {i}: {description}")
        print(f"   Montant à détecter: '{amount}'")

        # Créer une image avec le montant
        test_image = np.zeros((80, 200, 3), dtype=np.uint8)
        test_image[:] = (50, 50, 50)  # Fond gris foncé

        # Ajouter le texte avec "BB"
        full_text = f"{amount} BB"
        cv2.putText(test_image, full_text, (10, 50), cv2.FONT_HERSHEY_SIMPLEX, 1.2, (255, 255, 255), 2)

        try:
            # Tester la nouvelle détection de montant
            detected_amount = detector.detect_amount_text(test_image)
            print(f"   Résultat détecté: '{detected_amount}'")

            # Vérifier si le montant complet est détecté
            if detected_amount and amount in detected_amount:
                print(f"   ✅ SUCCÈS: Montant complet détecté!")
                success_count += 1
            elif detected_amount and any(digit in detected_amount for digit in amount.replace(',', '')):
                print(f"   ⚠️ PARTIEL: Une partie du montant détectée")
                success_count += 0.5
            else:
                print(f"   ❌ ÉCHEC: Montant non détecté ou incorrect")

        except Exception as e:
            print(f"   ❌ ERREUR: {e}")

    print(f"\n📊 RÉSULTATS FINAUX:")
    print(f"   Tests réussis: {success_count}/{len(test_cases)}")
    print(f"   Taux de réussite: {(success_count/len(test_cases)*100):.1f}%")

    if success_count >= len(test_cases) * 0.7:  # 70% de réussite
        print("✅ L'OCR amélioré fonctionne bien!")
        return True
    else:
        print("❌ L'OCR amélioré a encore des problèmes")
        return False

def test_comparaison_avant_apres():
    """Compare l'ancienne et la nouvelle méthode"""
    print("\n🧪 TEST DE COMPARAISON AVANT/APRÈS")
    print("=" * 60)

    # Charger la configuration
    config_path = os.path.abspath(os.path.join('..', 'Calibration', 'config', 'poker_advisor_config.json'))

    try:
        detector = Detector(config_path, use_cuda=True)
        print("✅ Détecteur initialisé")
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

    # Créer une image avec "28,9 BB"
    test_image = np.zeros((100, 250, 3), dtype=np.uint8)
    test_image[:] = (50, 50, 50)
    cv2.putText(test_image, "28,9 BB", (20, 60), cv2.FONT_HERSHEY_SIMPLEX, 1.5, (255, 255, 255), 2)

    print("\n🔍 Test avec l'image '28,9 BB':")

    try:
        # Tester la nouvelle méthode améliorée
        result_new = detector.detect_amount_text(test_image)
        print(f"   Nouvelle méthode (OCR amélioré): '{result_new}'")

        # Tester aussi la détection simple pour comparaison
        result_simple = detector.detect_text_simple(test_image, False)
        print(f"   Méthode simple (pour comparaison): '{result_simple}'")

        print(f"\n📊 COMPARAISON:")
        if result_new and "28" in result_new and "9" in result_new:
            print(f"   ✅ NOUVELLE MÉTHODE: Détecte le montant complet!")
        elif result_new and "9" in result_new:
            print(f"   ⚠️ NOUVELLE MÉTHODE: Détecte partiellement")
        else:
            print(f"   ❌ NOUVELLE MÉTHODE: Échec")

        if result_simple and "28" in result_simple and "9" in result_simple:
            print(f"   ✅ MÉTHODE SIMPLE: Détecte le montant complet")
        elif result_simple and "9" in result_simple:
            print(f"   ⚠️ MÉTHODE SIMPLE: Détecte partiellement")
        else:
            print(f"   ❌ MÉTHODE SIMPLE: Échec")

        # Déterminer si c'est une amélioration
        if result_new and len(result_new) > len(result_simple or ""):
            print(f"   🎉 AMÉLIORATION CONFIRMÉE!")
            return True
        else:
            print(f"   ⚠️ Pas d'amélioration significative")
            return False

    except Exception as e:
        print(f"❌ Erreur lors du test: {e}")
        return False

if __name__ == "__main__":
    print("🚀 DÉMARRAGE DES TESTS D'OCR AMÉLIORÉ")
    print()

    success1 = test_ocr_ameliore()
    success2 = test_comparaison_avant_apres()

    print("\n" + "=" * 60)
    if success1 and success2:
        print("🎉 TOUS LES TESTS RÉUSSIS!")
        print("L'OCR amélioré devrait maintenant détecter '28,9' au lieu de '9'.")
        print("\nTestez maintenant votre application:")
        print("1. Lancez lancer_detector_cuda_advisor.bat")
        print("2. Capturez l'écran avec la région problématique")
        print("3. Vous devriez voir '28,9' au lieu de '9'")
    else:
        print("❌ CERTAINS TESTS ONT ÉCHOUÉ")
        print("L'amélioration OCR peut avoir des problèmes.")

    input("\nAppuyez sur Entrée pour fermer...")
